/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * File Name          : freertos.c
  * Description        : Code for freertos applications
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2024 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "FreeRTOS.h"
#include "task.h"
#include "main.h"
#include "cmsis_os.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include <stdbool.h>
#include "elog.h"
#include "led_test.h"
#include "analog_sw_test.h"
#include "ad7606_test.h"
#include "bsp_rtc_test.h"
#include "fft_result_test.h"
#include "system_adaption.h"
#include "stack_monitor.h"
#include "stack_overflow_debug.h"
#include "stack_test.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN Variables */

/* USER CODE END Variables */
/* Definitions for defaultTask */
osThreadId_t defaultTaskHandle;
const osThreadAttr_t defaultTask_attributes = {
  .name = "defaultTask",
  .stack_size = 128 * 4,
  .priority = (osPriority_t) osPriorityNormal,
};

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN FunctionPrototypes */
void config_elog();
/* USER CODE END FunctionPrototypes */

void StartDefaultTask(void *argument);

void MX_FREERTOS_Init(void); /* (MISRA C 2004 rule 8.1) */

/**
  * @brief  FreeRTOS initialization
  * @param  None
  * @retval None
  */
void MX_FREERTOS_Init(void) {
  /* USER CODE BEGIN Init */
	config_elog();
//	elog_set_output_enabled(false);
  /* USER CODE END Init */

  /* USER CODE BEGIN RTOS_MUTEX */
  /* add mutexes, ... */
  /* USER CODE END RTOS_MUTEX */

  /* USER CODE BEGIN RTOS_SEMAPHORES */
  /* add semaphores, ... */
  /* USER CODE END RTOS_SEMAPHORES */

  /* USER CODE BEGIN RTOS_TIMERS */
  /* start timers, add new ones, ... */
  /* USER CODE END RTOS_TIMERS */

  /* USER CODE BEGIN RTOS_QUEUES */
  /* add queues, ... */
  /* USER CODE END RTOS_QUEUES */

  /* Create the thread(s) */
  /* creation of defaultTask */
  defaultTaskHandle = osThreadNew(StartDefaultTask, NULL, &defaultTask_attributes);

  /* USER CODE BEGIN RTOS_THREADS */
  /* add threads, ... */
  if( SYS_ADAPTION_OK != system_source_inst())
  {
    log_e("system source inst failed");
  }

  /* 创建栈监控任务 */
//  create_stack_monitor_task();

  /* 创建栈测试任务（可选，用于调试） */
  // create_stack_test_task();

  /* 执行栈溢出排查（可选，用于调试） */
  // stack_overflow_debug_main();
//  xTaskCreate(rtc_unit_test, 
//               "rtc_unit_test", 
//               256, 
//               NULL, 
//               osPriorityNormal3, 
//               NULL);
//	xTaskCreate(fft_result_test, 
//             "fft_result_test", 
//             256, 
//             NULL, 
//             osPriorityNormal, 
//             NULL);
//	xTaskCreate(fft_result_test, 
//               "fft_result_test", 
//               256, 
//               NULL, 
//               osPriorityNormal, 
//               NULL);
//	xTaskCreate(analog_sw_test, 
//               "analog_sw_test", 
//               256, 
//               NULL, 
//               osPriorityNormal, 
//               NULL);
//	xTaskCreate(ad7606_test, 
//               "ad7606_test", 
//               256, 
//               NULL, 
//               osPriorityNormal2, 
//               NULL);
			   
  /* USER CODE END RTOS_THREADS */

  /* USER CODE BEGIN RTOS_EVENTS */
  /* add events, ... */
  /* USER CODE END RTOS_EVENTS */

}

/* USER CODE BEGIN Header_StartDefaultTask */
/**
  * @brief  Function implementing the defaultTask thread.
  * @param  argument: Not used
  * @retval None
  */
/* USER CODE END Header_StartDefaultTask */
void StartDefaultTask(void *argument)
{
  /* USER CODE BEGIN StartDefaultTask */
  /* Infinite loop */
  for(;;)
  {
    osDelay(1);
  }
  /* USER CODE END StartDefaultTask */
}

/* Private application code --------------------------------------------------*/
/* USER CODE BEGIN Application */
void config_elog()
{
	elog_init();
	elog_set_fmt(ELOG_LVL_ERROR,   ELOG_FMT_LVL | ELOG_FMT_TAG | ELOG_FMT_TIME);
	elog_set_fmt(ELOG_LVL_WARN,    ELOG_FMT_LVL | ELOG_FMT_TAG | ELOG_FMT_TIME);
	elog_set_fmt(ELOG_LVL_INFO,    ELOG_FMT_LVL | ELOG_FMT_TAG | ELOG_FMT_TIME);
	elog_set_fmt(ELOG_LVL_DEBUG,   ELOG_FMT_ALL & ~ELOG_FMT_FUNC);
	elog_set_fmt(ELOG_LVL_VERBOSE, ELOG_FMT_ALL & ~ELOG_FMT_FUNC);
	elog_start();
}

/**
 * @brief 栈溢出钩子函数
 * @param xTask 发生栈溢出的任务句柄
 * @param pcTaskName 发生栈溢出的任务名称
 */
void vApplicationStackOverflowHook(TaskHandle_t xTask, char *pcTaskName)
{
    /* 禁用中断，防止进一步的系统损坏 */
    taskDISABLE_INTERRUPTS();

    /* 尝试输出错误信息（如果可能的话） */
    /* 注意：这里不能使用elog，因为可能导致递归调用 */

    /* 可以在这里设置断点进行调试 */
    /* 或者点亮LED指示错误 */

    /* 无限循环，等待调试器介入 */
    for(;;)
    {
        /* 可以在这里闪烁LED指示栈溢出 */
    }
}

/**
 * @brief 内存分配失败钩子函数
 */
void vApplicationMallocFailedHook(void)
{
    /* 禁用中断 */
    taskDISABLE_INTERRUPTS();

    /* 内存分配失败处理 */
    /* 可以在这里设置断点进行调试 */

    /* 无限循环，等待调试器介入 */
    for(;;)
    {
        /* 可以在这里闪烁LED指示内存分配失败 */
    }
}

/* USER CODE END Application */

