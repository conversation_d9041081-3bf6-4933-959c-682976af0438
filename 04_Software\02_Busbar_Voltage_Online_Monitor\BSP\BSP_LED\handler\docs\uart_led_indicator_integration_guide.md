# 串口LED指示器集成指南

## 概述

串口LED指示器模块提供了一个简单而强大的方式来指示串口数据接收状态。当串口接收到数据时，LED会立即亮起，并在数据接收结束后自动关闭。

## 核心特性

- **即时响应**：数据接收时LED立即亮起
- **自动关闭**：使用定时器机制，超时后LED自动关闭
- **线程安全**：基于LED处理器的事件驱动架构
- **防抖动**：连续接收数据时会刷新超时时间，避免LED频繁开关
- **可配置**：支持自定义超时时间和LED选择

## 工作原理

```
串口接收中断 -> uart_led_indicator_on_data_received() -> LED_MODE_TIMER_ON -> LED亮起
                                                                    |
                                                                    v
                                                            定时器倒计时
                                                                    |
                                                                    v
                                                            超时后LED自动关闭
```

### 状态机

```
IDLE ----[数据接收]----> RECEIVING ----[超时]----> IDLE
  ^                           |                      |
  |                           |                      |
  +----------[强制关闭]--------+                      |
  |                                                  |
  +------------------[超时]---------------------------+
```

## 快速集成步骤

### 1. 包含头文件

```c
#include "uart_led_indicator.h"
```

### 2. 初始化配置

```c
void uart_led_init(void)
{
    uart_led_indicator_config_t config = {
        .p_led_handler = &g_led_handler,        // LED处理器实例
        .led_which = LED_485,                   // 选择LED
        .timeout_ms = 100,                      // 100ms超时
        .p_timebase_interface = &g_timebase_interface
    };

    uart_led_indicator_ret_code_t ret = uart_led_indicator_init(&config);
    if (ret != UART_LED_INDICATOR_OK) {
        // 处理初始化失败
    }
}
```

### 3. 在串口接收中断中调用

```c
void USART1_IRQHandler(void)
{
    if (USART_GetITStatus(USART1, USART_IT_RXNE) != RESET) {
        uint8_t data = USART_ReceiveData(USART1);

        // 处理数据
        process_received_data(data);

        // 触发LED指示
        uart_led_indicator_on_data_received();

        USART_ClearITPendingBit(USART1, USART_IT_RXNE);
    }
}
```

### 4. 在主循环中调用周期性任务

```c
void main_loop(void)
{
    while (1) {
        // 周期性调用LED指示器任务
        uart_led_indicator_task();

        // 其他任务
        other_tasks();

        HAL_Delay(10);  // 10ms延时
    }
}
```

## 详细配置说明

### 配置参数

| 参数 | 类型 | 说明 | 范围 |
|------|------|------|------|
| `p_led_handler` | `bsp_led_handler_t*` | LED处理器实例指针 | 非空 |
| `led_which` | `led_which_t` | LED编号 | LED_RUN, LED_485, LED_POWER |
| `timeout_ms` | `uint32_t` | 超时时间(ms) | 10-5000 |
| `p_timebase_interface` | `uart_led_indicator_timebase_interface_t*` | 时基接口 | 非空 |

### 超时时间选择建议

| 应用场景 | 建议超时时间 | 说明 |
|----------|--------------|------|
| Modbus RTU | 50-100ms | 数据包较短，响应快 |
| 调试串口 | 100-200ms | 可能有连续输出 |
| 文件传输 | 200-500ms | 数据流较长 |
| 低速通信 | 500-1000ms | 波特率较低 |

## 性能优化

### 1. 避免频繁调用

```c
// 好的做法：只在真正接收到数据时调用
void UART_IRQHandler(void)
{
    if (UART_GetITStatus(UART, UART_IT_RXNE)) {
        uint8_t data = UART_ReceiveData(UART);
        uart_led_indicator_on_data_received();  // 只在接收时调用
    }
}

// 避免的做法：在其他中断中也调用
void UART_IRQHandler(void)
{
    uart_led_indicator_on_data_received();  // 错误：无论什么中断都调用

    if (UART_GetITStatus(UART, UART_IT_RXNE)) {
        uint8_t data = UART_ReceiveData(UART);
    }
}
```

### 2. 合理设置调用频率

```c
// 主循环中的调用频率建议
void main_loop(void)
{
    while (1) {
        uart_led_indicator_task();  // 10ms调用一次足够
        HAL_Delay(10);
    }
}
```

## 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方法 |
|--------|------|----------|
| `UART_LED_INDICATOR_ERROR_PARAM` | 参数错误 | 检查配置参数是否正确 |
| `UART_LED_INDICATOR_ERROR_NOT_INIT` | 未初始化 | 先调用初始化函数 |
| `UART_LED_INDICATOR_ERROR_ALREADY_INIT` | 重复初始化 | 避免重复调用初始化 |

### 错误处理示例

```c
uart_led_indicator_ret_code_t ret = uart_led_indicator_init(&config);
switch (ret) {
    case UART_LED_INDICATOR_OK:
        printf("初始化成功\n");
        break;
    case UART_LED_INDICATOR_ERROR_PARAM:
        printf("参数错误，请检查配置\n");
        break;
    case UART_LED_INDICATOR_ERROR_ALREADY_INIT:
        printf("已经初始化，无需重复初始化\n");
        break;
    default:
        printf("未知错误: %d\n", ret);
        break;
}
```

## 高级用法

### 1. 动态调整超时时间

```c
// 根据通信协议动态调整
void set_protocol_timeout(protocol_type_t protocol)
{
    uint32_t timeout_ms;

    switch (protocol) {
        case PROTOCOL_MODBUS:
            timeout_ms = 50;
            break;
        case PROTOCOL_DEBUG:
            timeout_ms = 200;
            break;
        default:
            timeout_ms = 100;
            break;
    }

    // 重新初始化以应用新的超时时间
    uart_led_indicator_deinit();

    uart_led_indicator_config_t config = {
        .p_led_handler = &g_led_handler,
        .led_which = LED_485,
        .timeout_ms = timeout_ms,
        .p_timebase_interface = &g_timebase_interface
    };

    uart_led_indicator_init(&config);
}
```

### 2. 状态监控

```c
void monitor_uart_status(void)
{
    uart_led_indicator_state_t state = uart_led_indicator_get_state();

    switch (state) {
        case UART_LED_INDICATOR_STATE_IDLE:
            printf("串口空闲\n");
            break;
        case UART_LED_INDICATOR_STATE_RECEIVING:
            printf("串口正在接收数据\n");
            break;
    }
}
```

## 注意事项

1. **中断安全**：`uart_led_indicator_on_data_received()` 可以在中断中安全调用
2. **任务调用**：`uart_led_indicator_task()` 必须在主循环或RTOS任务中周期性调用
3. **单实例**：当前实现为单实例，如需多串口支持需要扩展
4. **依赖关系**：依赖LED处理器模块，确保LED处理器已正确初始化
5. **时基精度**：时基接口的精度影响超时检测的准确性

## 故障排除

### LED不亮

1. 检查LED处理器是否已初始化
2. 检查LED编号是否正确注册
3. 检查是否正确调用了`uart_led_indicator_on_data_received()`

### LED不关闭

1. 检查是否在主循环中调用了`uart_led_indicator_task()`
2. 检查时基接口是否正常工作
3. 检查超时时间设置是否合理

### 性能问题

1. 减少`uart_led_indicator_task()`的调用频率
2. 避免在高频中断中调用
3. 检查LED处理器的事件队列是否溢出