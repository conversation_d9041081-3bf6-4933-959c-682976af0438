      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name
      3360          0       3702          4          0          0   ad7606_driver.o
        50          0          0          0          0          0   algorithm.o
      3448          0       5516         20       6184          0   bsp_adc_collect_xxx_handler.o
      1802          0        325          0          0          0   bsp_led_driver.o
      1884          0          0          8        240          0   bsp_led_handler.o
      2600          0       1890         21        456          0   cm_backtrace.o
        12          0          0          0          0          0   cmb_fault.o
       430          0          0          4       1728          0   cmsis_os2.o
        84          0          0          8          0          0   crc.o
       896          0         15        328        240          0   data_proc_calcu_fun.o
      1232          0        121          4         32          0   data_process_task.o
       104          0          0          0          0          0   dma.o
      2800          0        297         52       1272          0   elog.o
       720          0          0         23       4096          0   elog_async.o
       644          0          0         20         12          0   elog_port_improved.o
       256          0         35          0          0          0   elog_utils.o
       788          0          0          0          0          0   event_groups.o
     10534          0       1010          4      37136          0   fft_calculate_task.o
      1372          0        745          0          0          0   flash_event_fun.o
      1712          0        259         20         60          0   flash_event_handler.o
       230          0         65          4          0          0   freertos.o
       356          0          0          0          0          0   gpio.o
       732          0          0         32      30720          0   heap_4.o
        88          0          0          0          0          0   linked_list.o
       148          0          0          0          0          0   list.o
       356          0        156          0        176          0   lpf_fir_alogorithm.o
       404          0          0          0          0          0   main.o
       528          0          0         20         20          0   mcu_flash_driver.o
      1436          0         85          0          0          0   mcu_gpio_driver.o
      2880          0       1614         12          0          0   mcu_spi_driver.o
        88          0          0          0         32          0   mcu_uart_driver.o
      3718          0        110         22         20          0   modbus_rtu.o
        70          0          0          0          0          0   os_critical.o
       852          0        151          0          0          0   os_event_group.o
       816          0         63          0          0          0   os_queue.o
      1104          0         99          0          0          0   os_semaphore.o
       186          0          0          0          0          0   os_task.o
       720          0         81          0          0          0   os_timer.o
      1014          0          0         12          0          0   port.o
      3060          0          0          0         64          0   queue.o
       760          0        157          0          0          0   reg_adress_msg.o
      3212          0       1713          0          0          0   ringbuff.o
       366          0         16         52          0          0   rs485_dev.o
      3706          0        438         56        420          0   rs485_event_handler.o
       196          0          0          0         32          0   rtc.o
       554          0         33          0       4280          0   segger_rtt.o
      1096          0         16          0          0          0   segger_rtt_printf.o
       500          0          0          0        280          0   spi.o
         0          0          0          0          0          0   stack_monitor.o
        36          0        408          0       1024          0   startup_stm32f411xe.o
        96          0          0          9          0          0   stm32f4xx_hal.o
       636          0          0          0          0          0   stm32f4xx_hal_cortex.o
       112          0          0          0          0          0   stm32f4xx_hal_crc.o
      2482          0          8          0          0          0   stm32f4xx_hal_dma.o
      1208          0          0          0         32          0   stm32f4xx_hal_flash.o
       792          0          0          0          0          0   stm32f4xx_hal_flash_ex.o
      1720          0          0          0          0          0   stm32f4xx_hal_gpio.o
        84          0          0          0          0          0   stm32f4xx_hal_msp.o
      2584          0          0          0          0          0   stm32f4xx_hal_rcc.o
       988          0          0          0          0          0   stm32f4xx_hal_rcc_ex.o
      1680          0          0          0          0          0   stm32f4xx_hal_rtc.o
      6492          0          0          0          0          0   stm32f4xx_hal_spi.o
      4520          0          0          0          0          0   stm32f4xx_hal_tim.o
       356          0          0          0          0          0   stm32f4xx_hal_tim_ex.o
       180          0          0          0         72          0   stm32f4xx_hal_timebase_tim.o
      3172          0          0          0          0          0   stm32f4xx_hal_uart.o
       260          0          0          0          0          0   stm32f4xx_it.o
         0          0          0          0          0          0   stream_buffer.o
      9564          0       1198        884        704          0   system_adaption.o
        20          0         24          4          0          0   system_stm32f4xx.o
      4634          0          0         60       1220          0   tasks.o
       736          0          0          0        216          0   tim.o
      1462          0          0         20        280          0   timers.o
       506          0          6         28        148          0   uart_dev.o
       392          0        271          0         32          0   uart_led_indicator.o
       340          0          0          0        264          0   usart.o
      6448          0      13860          0          0          0   arm_cortexM4lf_math.lib
Object Totals

Memory Map of the image

	Load Region LR_IROM1 

		Execution Region ER_IROM1 (Exec base: 0x08000000, Size: 0x00026EAC, Max: 0x00080000, END)

		Execution Region RW_IRAM1 (Exec base: 0x20000000, Size: 0x00016C38, Max: 0x00020000, END)

Image component sizes