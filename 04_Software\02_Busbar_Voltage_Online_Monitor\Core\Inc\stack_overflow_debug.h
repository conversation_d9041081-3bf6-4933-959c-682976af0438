/**
 * @file stack_overflow_debug.h
 * @brief FreeRTOS栈溢出排查示例和调试工具头文件
 * <AUTHOR> Assistant
 * @date 2024
 */

#ifndef __STACK_OVERFLOW_DEBUG_H__
#define __STACK_OVERFLOW_DEBUG_H__

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 栈溢出排查步骤示例
 */
void stack_overflow_debug_guide(void);

/**
 * @brief 创建栈溢出测试任务
 * 警告：此函数会创建一个故意造成栈溢出的任务
 */
void create_stack_overflow_test_task(void);

/**
 * @brief 分析任务栈使用模式
 * @param pcTaskName 任务名称
 */
void analyze_task_stack_pattern(const char* pcTaskName);

/**
 * @brief 检查系统整体内存使用情况
 */
void check_system_memory_usage(void);

/**
 * @brief 栈溢出排查主函数
 */
void stack_overflow_debug_main(void);

#ifdef __cplusplus
}
#endif

#endif /* __STACK_OVERFLOW_DEBUG_H__ */
