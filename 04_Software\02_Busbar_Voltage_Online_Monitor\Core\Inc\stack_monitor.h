/**
 * @file stack_monitor.h
 * @brief FreeRTOS栈使用情况监控工具头文件
 * <AUTHOR> Assistant
 * @date 2024
 */

#ifndef __STACK_MONITOR_H__
#define __STACK_MONITOR_H__

#include "FreeRTOS.h"
#include "task.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 获取任务栈使用情况
 * @param xTask 任务句柄，NULL表示当前任务
 * @return 剩余栈空间（以字为单位）
 */
UBaseType_t get_task_stack_high_water_mark(TaskHandle_t xTask);

/**
 * @brief 打印单个任务的栈使用情况
 * @param xTask 任务句柄
 * @param pcTaskName 任务名称
 */
void print_task_stack_usage(TaskHandle_t xTask, const char* pcTaskName);

/**
 * @brief 打印所有任务的栈使用情况
 */
void print_all_tasks_stack_usage(void);

/**
 * @brief 检查特定任务的栈使用情况
 * @param pcTaskName 任务名称
 * @return pdTRUE: 栈使用正常, pdFALSE: 栈使用异常
 */
BaseType_t check_task_stack_safety(const char* pcTaskName);

/**
 * @brief 栈监控任务（可选）
 * @param pvParameters 任务参数
 */
void stack_monitor_task(void *pvParameters);

/**
 * @brief 创建栈监控任务
 * @return pdPASS: 创建成功, pdFAIL: 创建失败
 */
BaseType_t create_stack_monitor_task(void);

/* 便捷宏定义 */
#define CHECK_CURRENT_TASK_STACK() \
    do { \
        UBaseType_t uxHighWaterMark = uxTaskGetStackHighWaterMark(NULL); \
        if (uxHighWaterMark < 16) { \
            printf("[STACK] CRITICAL: Current task has only %d bytes of stack remaining!\r\n", \
                  (int)uxHighWaterMark * 4); \
        } \
    } while(0)

#define PRINT_CURRENT_TASK_STACK() \
    do { \
        TaskHandle_t xCurrentTask = xTaskGetCurrentTaskHandle(); \
        char* pcTaskName = pcTaskGetName(xCurrentTask); \
        print_task_stack_usage(xCurrentTask, pcTaskName); \
    } while(0)

#ifdef __cplusplus
}
#endif

#endif /* __STACK_MONITOR_H__ */
