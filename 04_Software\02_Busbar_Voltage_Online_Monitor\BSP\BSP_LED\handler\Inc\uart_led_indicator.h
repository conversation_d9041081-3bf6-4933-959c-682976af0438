/******************************************************************************
 * @file uart_led_indicator.h
 * @brief 串口数据接收LED指示器头文件
 * <AUTHOR>
 * @version 1.0
 * @date 2024-11-01
 *
 * @copyright Copyright (c) 2024
 *
 * Processing flow:
 * 1. 串口接收中断触发时调用uart_led_indicator_on_data_received()
 * 2. LED立即亮起，启动接收超时定时器
 * 3. 持续接收数据时重新刷新定时器
 * 4. 超时后LED自动关闭
 *
 * @par Usage Example
 * ```c
 * // 初始化
 * uart_led_indicator_config_t config = {
 *     .p_led_handler = &g_led_handler,
 *     .led_which = LED_485,
 *     .timeout_ms = 100,
 *     .p_timebase_interface = &g_timebase_interface
 * };
 * uart_led_indicator_init(&config);
 * 
 * // 在串口接收中断中调用
 * void UART_IRQHandler(void) {
 *     if (UART_GetITStatus(UART, UART_IT_RXNE)) {
 *         uint8_t data = UART_ReceiveData(UART);
 *         // 处理数据...
 *         uart_led_indicator_on_data_received();
 *     }
 * }
 * ```
 *
 * @note 1 tab == 4 spaces!
 *
 *****************************************************************************/
#ifndef __UART_LED_INDICATOR_H_
#define __UART_LED_INDICATOR_H_

//******************************** Includes *********************************//
#include <stdint.h>
#include <stdbool.h>
#include "bsp_led_handler.h"
//******************************** Includes *********************************//

//******************************** Defines **********************************//
#define UART_LED_INDICATOR_DEFAULT_TIMEOUT_MS    (100)    /* 默认超时时间100ms */
#define UART_LED_INDICATOR_MIN_TIMEOUT_MS        (10)     /* 最小超时时间10ms */
#define UART_LED_INDICATOR_MAX_TIMEOUT_MS        (5000)   /* 最大超时时间5秒 */
//******************************** Defines **********************************//

//******************************** Types ************************************//
/* 返回码定义 */
typedef enum
{
    UART_LED_INDICATOR_OK = 0,              /* 成功 */
    UART_LED_INDICATOR_ERROR,               /* 一般错误 */
    UART_LED_INDICATOR_ERROR_PARAM,         /* 参数错误 */
    UART_LED_INDICATOR_ERROR_NOT_INIT,      /* 未初始化 */
    UART_LED_INDICATOR_ERROR_ALREADY_INIT,  /* 已经初始化 */
}uart_led_indicator_ret_code_t;

/* 时基接口定义 */
typedef struct
{
    uint32_t (*pfget_timetick_ms)(void);    /* 获取系统时间戳(ms) */
}uart_led_indicator_timebase_interface_t;

/* LED状态变化回调函数类型 */
typedef void (*uart_led_indicator_state_callback_t)(uart_led_indicator_state_t new_state, void *p_user_data);

/* 配置参数结构体 */
typedef struct uart_led_indicator_config
{
    bsp_led_handler_t                        *p_led_handler;        /* LED处理器指针 */
    led_which_t                              led_which;             /* LED编号 */
    uint32_t                                 timeout_ms;            /* 接收超时时间(ms) */
    uart_led_indicator_timebase_interface_t  *p_timebase_interface; /* 时基接口 */
    uart_led_indicator_state_callback_t      state_callback;        /* 状态变化回调函数(可选) */
    void                                     *p_user_data;          /* 用户数据指针(可选) */
}uart_led_indicator_config_t;

/* 状态枚举 */
typedef enum
{
    UART_LED_INDICATOR_STATE_IDLE = 0,      /* 空闲状态 */
    UART_LED_INDICATOR_STATE_RECEIVING,     /* 接收状态 */
}uart_led_indicator_state_t;
//******************************** Types ************************************//

//******************************** Functions ********************************//
/**
 * @brief 初始化串口LED指示器
 * 
 * @param p_config 配置参数指针
 * @return uart_led_indicator_ret_code_t 返回码
 */
uart_led_indicator_ret_code_t uart_led_indicator_init(uart_led_indicator_config_t *p_config);

/**
 * @brief 反初始化串口LED指示器
 * 
 * @return uart_led_indicator_ret_code_t 返回码
 */
uart_led_indicator_ret_code_t uart_led_indicator_deinit(void);

/**
 * @brief 数据接收事件处理（在串口接收中断中调用）
 * 
 * @return uart_led_indicator_ret_code_t 返回码
 */
uart_led_indicator_ret_code_t uart_led_indicator_on_data_received(void);

/**
 * @brief 周期性任务处理（需要在主循环或定时器中调用）
 * @deprecated 建议使用事件驱动模式，此函数将在未来版本中移除
 * @return uart_led_indicator_ret_code_t 返回码
 */
uart_led_indicator_ret_code_t uart_led_indicator_task(void);

/**
 * @brief LED状态变化通知（由bsp_led_handler回调）
 * @note 此函数由LED处理器在LED状态变化时自动调用，用户无需手动调用
 * @param led_which LED编号
 * @param mode LED模式
 * @param p_param 参数指针
 */
void uart_led_indicator_on_led_state_changed(led_which_t led_which, led_control_mode_t mode, void *p_param);

/**
 * @brief 获取当前状态
 * 
 * @return uart_led_indicator_state_t 当前状态
 */
uart_led_indicator_state_t uart_led_indicator_get_state(void);

/**
 * @brief 强制关闭LED指示
 * 
 * @return uart_led_indicator_ret_code_t 返回码
 */
uart_led_indicator_ret_code_t uart_led_indicator_force_off(void);

//******************************** Functions ********************************//

#endif // __UART_LED_INDICATOR_H_
