/******************************************************************************
 * @file bsp_led_driver.c
 * @brief 
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-30
 * 
 * @copyright Copyright (c) 2024 
 * 
 * Processing flow:
 * 
 * @par dependencies
 * 
 * @note 1 tab == 4 spaces!
 * 
 *****************************************************************************/

//******************************** Includes *********************************//
#include "bsp_led_driver.h"


//******************************** Includes *********************************//


//******************************** Defines **********************************//
#define BSP_LED_DRIVER_ON 	  pled_data->led_gpio_interface->pfgpio_write_pin(\
			pled_data->led_gpio_interface->pgpio_handler, LED_GPIO_HIGH_LEVEL)
#define BSP_LED_DRIVER_OFF 	  pled_data->led_gpio_interface->pfgpio_write_pin(\
			pled_data->led_gpio_interface->pgpio_handler, LED_GPIO_LOW_LEVEL)
#define BSP_LED_DRIVER_TOGGLE pled_data->led_gpio_interface->pfgpio_toggle_pin(\
			pled_data->led_gpio_interface->pgpio_handler)

typedef enum 
{
	LED_DRIVER_NOT_INITED = 0,
	LED_DRIVER_INITED     = 1,
}led_driver_inited_t;

typedef struct  led_private_data_t
{
	led_driver_inited_t  	 inited;
	led_gpio_interface_t 	 *led_gpio_interface;
	led_which_t 			 led_which;
 	led_dynamic_allocation_t *pdynamic_allocation;
}led_private_data_t;
#ifndef LED_DRIVER_USE_DYNAMIC_ALLOCATION
static led_private_data_t g_led_private_data[LED_DEV_Quantity];
#endif

/******************************************************************************
 * @brief led driver初始化
 * 
 * @param  pled_driver 对象指针     
 * 
 * @return led_driver_ret_code_t 
 * @retval LED_DRIVER_OK 成功
 * @retval LED_DRIVER_ERROR 失败
 * @retval LED_DRIVER_ERRORRESOURCE 资源错误
 *****************************************************************************/
led_driver_ret_code_t bsp_led_driver_init(bsp_led_driver_t *pled_driver)
{
	log_d("led driver init start");
	/* 0.检查输入对象的合法性 */
	if((NULL == pled_driver)
	|| (NULL ==  pled_driver->private_data))
	{
		log_e("bsp_led_init: error input object, file:%s, line:%d",\
			 											__FILE__, __LINE__);
		return LED_DRIVER_ERRORRESOURCE;
	}
	/*	1.检查是否进行过初始化	*/
	led_private_data_t *pled_data = pled_driver->private_data;
	if(LED_DRIVER_INITED == pled_data->inited)
	{	
		log_e("bsp_led_init: led driver has been inited, file:%s, line:%d",\
			 											__FILE__, __LINE__);
		return LED_DRIVER_ERROR;
	}
	/* 2.初始化led gpio接口 */
	pled_data->led_gpio_interface->pfgpio_init(pled_data->led_gpio_interface);
	pled_data->inited = LED_DRIVER_INITED;
	log_d("led driver init end");
	return LED_DRIVER_OK;
}

/******************************************************************************
 * @brief led driver反初始化
 * 
 * @param  pled_driver led对象    
 * 
 * @return led_driver_ret_code_t 
 * @retval LED_DRIVER_OK 成功
 * @retval LED_DRIVER_ERROR 失败
 * @retval LED_DRIVER_ERRORRESOURCE 资源错误
 *****************************************************************************/
led_driver_ret_code_t bsp_led_driver_deinit(bsp_led_driver_t *pled_driver)
{
	log_d("led driver deinit start");
	/* 0.检查输入对象的合法性 */
	if((NULL == pled_driver)
	|| (NULL ==  pled_driver->private_data))
	{
		log_e("deinit: error input object, file:%s, line:%d",\
			 											__FILE__, __LINE__);
		return LED_DRIVER_ERRORRESOURCE;
	}
	/* 1.进行led gpio接口的反初始化 */
	led_private_data_t *pled_data = pled_driver->private_data;
	pled_data->led_gpio_interface->pfgpio_deinit(pled_data->led_gpio_interface);
	/* 2.清空led_private_data_t */
#ifdef LED_DRIVER_USE_DYNAMIC_ALLOCATION
	if(NULL != pled_data->pdynamic_allocation)
	{
		pled_data->pdynamic_allocation->pffree(pled_data->pdynamic_allocation);
		pled_data->pdynamic_allocation = NULL;
	}
#endif
	pled_driver->private_data = NULL;
	pled_data->inited = LED_DRIVER_NOT_INITED;
	log_d("led driver deinit end");
	return LED_DRIVER_OK;
}

/******************************************************************************
 * @brief 控制led
 * 
 * @param  pled_driver led驱动对象     
 * @param  statue      led状态     
 * 
 * @return led_driver_ret_code_t 
 * @retval LED_DRIVER_OK 成功
 * @retval LED_DRIVER_ERROR 失败
 * @retval LED_DRIVER_ERRORRESOURCE 资源错误
 *****************************************************************************/
led_driver_ret_code_t bsp_led_driver_control( 	bsp_led_driver_t *pled_driver,
                                				led_statue_t statue)
{
	/* 0.检查输入对象的合法性 */
	if((NULL == pled_driver)
	|| (NULL ==  pled_driver->private_data))
	{
		log_e("led_control: error input object, file:%s, line:%d",\
			 											__FILE__, __LINE__);
		return LED_DRIVER_ERRORRESOURCE;
	}
	/* 1.led控制 */
	led_private_data_t *pled_data = pled_driver->private_data;
	switch(statue)
	{
		case LED_OFF: 
		{
			BSP_LED_DRIVER_OFF;
		}break;
		case LED_ON: 
		{
			BSP_LED_DRIVER_ON;
		}break;
		case LED_TOGGLE: 
		{
			BSP_LED_DRIVER_TOGGLE;
		}break;
		default:
		{
			log_e("led_control: error input statue:%d, file:%s, line:%d",statue,\
			 											__FILE__, __LINE__);
			return LED_DRIVER_ERRORPARAMETER;
		}
	}
	return LED_DRIVER_OK;
}


/******************************************************************************
 * @brief 控制led的亮度，待实现
 * 
 * @param  pled_driver  led驱动对象     
 * @param  brightness   亮度    
 * 
 * @return led_driver_ret_code_t 
 * 
 *****************************************************************************/
led_driver_ret_code_t bsp_led_driver_set_brightness ( bsp_led_driver_t *pled_driver,
                                    					int brightness)
{
	return LED_DRIVER_OK;
}

/******************************************************************************
 * @brief Get the led which object 获取led是哪盏
 * 
 * @param  pled_driver      
 * @param  led_which        
 * 
 * @return led_driver_ret_code_t 
 *****************************************************************************/
led_driver_ret_code_t bsp_led_driver_get_led_which(	struct bsp_led_driver_t *pled_driver,
                                    				led_which_t *led_which)
{

	/* 0.检查输入对象的合法性 */
	if((NULL == pled_driver)
	|| (NULL ==  pled_driver->private_data))
	{
		log_e("get_led_which: error input object, file:%s, line:%d",\
			 											__FILE__, __LINE__);
		return LED_DRIVER_ERRORRESOURCE;
	}
	/* 1.检查输入参数 */
	if(NULL == led_which)
	{
		log_e("get_led_which: error input led_which, file:%s, line:%d",\
			 											__FILE__, __LINE__);
		return LED_DRIVER_ERRORPARAMETER;
	}
	/* 2.获取led_which */
	led_private_data_t *pled_data = pled_driver->private_data;
	*led_which = pled_data->led_which;
	log_d("led_which is %d",*led_which);
	return LED_DRIVER_OK;
}


/******************************************************************************
 * @brief led driver实例化
 * 
 * @param  pled_driver  led driver对象     
 * @param  p_gpio_interface  led gpio接口     
 * @param  p_dynamic_allocation 动态分配内存接口     
 * @param  led_which  led编号     
 * 
 * @retval LED_DRIVER_OK 成功
 * @retval LED_DRIVER_ERROR 失败
 * @retval LED_DRIVER_ERRORPARAMETER 参数错误
 * @retval LED_DRIVER_ERRORRESOURCE 资源错误
 * @retval LED_DRIVER_ERRORNOMEMORY 内存错误
 *****************************************************************************/
led_driver_ret_code_t bsp_led_driver_inst(
                                          bsp_led_driver_t         *pled_driver, 
                                          led_gpio_interface_t     *p_gpio_interface,
#ifdef LED_DRIVER_USE_DYNAMIC_ALLOCATION
                                          led_dynamic_allocation_t *p_dynamic_allocation,
#endif //LED_DRIVER_USE_DYNAMIC_ALLOCATION
                                          led_which_t              led_which
                                          )
{
	log_d("led driver instance start");
	/* 0.检查输入对象的合法性 */
	if(NULL == pled_driver)
	{
		log_e(" bsp_led_driver_inst error input object, file:%s, line:%d",\
			 											__FILE__, __LINE__);
	}
	/* 1. 检查输入参数 */
	if((NULL == p_gpio_interface) 		||
#ifdef LED_DRIVER_USE_DYNAMIC_ALLOCATION
		(NULL == p_dynamic_allocation)  ||
#endif //LED_DRIVER_USE_DYNAMIC_ALLOCATION
		(led_which >=LED_DEV_Quantity))
	{
		log_e("bsp_led_driver_inst: error input params, file:%s, line:%d",\
			 											__FILE__, __LINE__);
		return  LED_DRIVER_ERRORPARAMETER;
	}
	/* 1.1 检查p_gpio_interface接口 */
	if(	
		(NULL == p_gpio_interface->pfgpio_init)      ||
		(NULL == p_gpio_interface->pfgpio_deinit)    ||
		(NULL == p_gpio_interface->pfgpio_write_pin) ||
		(NULL == p_gpio_interface->pfgpio_read_pin)  ||
		(NULL == p_gpio_interface->pfgpio_toggle_pin)
		)
	{
		log_e("bsp_led_driver_inst: error input gpio interface, file:%s, line:%d",\
			 											__FILE__, __LINE__);
		return  LED_DRIVER_ERRORPARAMETER;
	}
	/* 1.2检查p_gpio_interface */
#ifdef LED_DRIVER_USE_DYNAMIC_ALLOCATION
	if(	(NULL == p_dynamic_allocation->pfmalloc) ||
		(NULL == p_dynamic_allocation->pffree))
	{
		log_e("bsp_led_driver_inst: error input dynamic allocation, file:%s, line:%d",\
			 											__FILE__, __LINE__);
		return  LED_DRIVER_ERRORPARAMETER;
	}
	/* 2.申请led_private_data_t资源 */
	pled_driver->private_data = (led_private_data_t *)p_dynamic_allocation->pfmalloc(sizeof(led_private_data_t));
	if(NULL == pled_driver->private_data)
	{
		log_e("bsp_led_driver_inst: error malloc led_private_data_t, file:%s, line:%d",\
			 											__FILE__, __LINE__);
		return  LED_DRIVER_ERRORNOMEMORY;
	}
	/* 3.初始化私有数据 */
	pled_driver->private_data->led_gpio_interface  = p_gpio_interface;
	pled_driver->private_data->pdynamic_allocation = p_dynamic_allocation;
#else
	pled_driver->private_data = &g_led_private_data[led_which];
#endif //LED_DRIVER_USE_DYNAMIC_ALLOCATION

	pled_driver->private_data->led_which = led_which;
	  /* 4.初始化led driver,挂载函数*/
	pled_driver->pfinit           = bsp_led_driver_init;
	pled_driver->pfdeinit         = bsp_led_driver_deinit;
	pled_driver->pfled_control    = bsp_led_driver_control;
	pled_driver->pfset_brightness = bsp_led_driver_set_brightness;
	pled_driver->pfget_led_which  = bsp_led_driver_get_led_which;
	bsp_led_driver_init(pled_driver);
	log_d("led driver instance end");
	return LED_DRIVER_OK;
}