[2025-07-31 23:09:16]	compilation failed at : "d:\arrester_insulation_monitoring\04_Software\02_Busbar_Voltage_Online_Monitor\SYSTEM\Src\system_adaption.c", exit code: 1
command: 
  "C:\Keil_v5\ARM\ARMCC\bin\armcc.exe" -c --apcs=interwork -I. -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../BSP/BSP_AD7606/hal_driver/Src -I../BSP/BSP_AD7606/hal_driver/Inc -I../MCU_Peripherals_Drivers/Inc -I../MCU_Peripherals_Drivers/Src -I../Middlewares/linked_list -I../easylogger/inc -I../easylogger/src -I../easylogger/port -I../RTT/Config -I../RTT/RTT -I../BSP/BSP_LED/hal_driver/Inc -I../BSP/BSP_LED/hal_driver/Src -I../unit_test/Inc -I../unit_test/Src -I../BSP/BSP_Analog_Switch/hal_driver/Inc -I../BSP/BSP_Analog_Switch/hal_driver/Src -I../BSP/BSP_AD7606/handler/Inc -I../BSP/BSP_AD7606/handler/Src -I../BSP/BSP_Analog_Switch/handler/Inc -I../BSP/BSP_Analog_Switch/handler/Src -I../Middlewares/cm_backtrace -I../Middlewares/cm_backtrace/Languages/en-US -I../Middlewares/cm_backtrace/Languages/zh-CN -I../Tasks/Data_Process_Task/Inc -I../Tasks/Data_Process_Task/Src -I../Tasks/FFT_Task/Inc -I../Tasks/FFT_Task/Src -I../SYSTEM/Inc -I../SYSTEM/Src -I../Middlewares/Algorithm -I../Drivers/CMSIS/DSP/Include -I../Drivers/CMSIS/DSP/PrivateInclude -I../Drivers/CMSIS/DSP/Source/TransformFunctions -I../BSP/BSP_RTC/hal_driver/Inc -I../BSP/BSP_RTC/hal_driver/Src -I../BSP/BSP_RTC/handler/Inc -I../BSP/BSP_RTC/handler/Src -I../Tasks/Thunderstrike_Task/Inc -I../Tasks/Thunderstrike_Task/Src -I../Middlewares/RingBuff/Inc -I../Middlewares/RingBuff/Src -I../Middlewares/Systemview/Config -I../Middlewares/Systemview/Sample/FreeRTOSV10.4 -I../Middlewares/Systemview/SEGGER -I../Drivers/CMSIS/DSP/Source/FastMathFunctions -I../Middlewares/LPF_FIR_Algorithm/Src -I../Middlewares/LPF_FIR_Algorithm/Inc -I../Busbar_Voltage_Online_Monitor/Flash/handler/Inc -I../Busbar_Voltage_Online_Monitor/Flash/handler/Src -I../Busbar_Voltage_Online_Monitor/RS485/hal_driver/Inc -I../Busbar_Voltage_Online_Monitor/RS485/hal_driver/Src -I../Busbar_Voltage_Online_Monitor/RS485/handler/Inc -I../Busbar_Voltage_Online_Monitor/RS485/handler/Src -I../Busbar_Voltage_Online_Monitor/Uart_dev/Inc -I../Busbar_Voltage_Online_Monitor/Uart_dev/Src -I.cmsis/include -I../MDK-ARM/RTE/_Busbar_Voltage_Online_Monitor -I../Middlewares/OSAL/src -I../Middlewares/OSAL/inc -I../BSP/BSP_LED/handler/Inc -I../BSP/BSP_LED/handler/Src -D"USE_HAL_DRIVER" -D"STM32F411xE" -D"USE_FULL_ASSERT" -D"ARM_MATH_CM4" -D"__CC_ARM" -D"ARM_MATH_MATRIX_CHECK" -D"ARM_MATH_ROUNDING" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=2803,1,1035 -g -o .\build\Busbar_Voltage_Online_Monitor\.obj\__\SYSTEM\Src\system_adaption.o --no_depend_system_headers --depend .\build\Busbar_Voltage_Online_Monitor\.obj\__\SYSTEM\Src\system_adaption.d .\..\SYSTEM\Src\system_adaption.c
   at unify_builder.Program.compileByMulThread(Int32 thrNum, CmdInfo[] cmds_, List`1 errLogs)
   at unify_builder.Program.Main(String[] args_)
---
"../MCU_Peripherals_Drivers/Inc/mcu_spi_driver.h", line 43: Warning:  #47-D: incompatible redefinition of macro "MCU_SPI_DRIVER_DEBUG"  (declared at line 94 of "../SYSTEM/Inc/system_config.h")
  #define MCU_SPI_DRIVER_DEBUG(...)
          ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 42: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_DEBUG"  (declared at line 88 of "../SYSTEM/Inc/system_config.h")
      #define AD7606_DRIVER_DEBUG(...) printf(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 53: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_LOG_IRQ"  (declared at line 38)
      #define AD7606_DRIVER_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/handler/Inc/bsp_adc_collect_xxx_handler.h", line 58: Warning:  #47-D: incompatible redefinition of macro "ADC_COLLECT_LOG_IRQ"  (declared at line 39)
  #define ADC_COLLECT_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
          ^
".\..\SYSTEM\Src\system_adaption.c", line 756: Warning:  #550-D: variable "fft_calcu_adc_arg"  was set but never used
      static fft_calcu_adc_arg_t fft_calcu_adc_arg;
                                 ^
".\..\SYSTEM\Src\system_adaption.c", line 897: Warning:  #550-D: variable "ac_phase_data"  was set but never used
      static float ac_phase_data = 0.0f;
                   ^
".\..\SYSTEM\Src\system_adaption.c", line 1552: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, const uint8_t *, uint16_t, uint32_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t, uint32_t)"
                           HAL_UART_Transmit,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1553: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, const uint8_t *, uint16_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t)"
                           HAL_UART_Transmit_IT,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1554: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, uint8_t *, uint16_t, uint32_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t, uint32_t)"
                           HAL_UART_Receive,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1555: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, uint8_t *, uint16_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t)"
                           HAL_UART_Receive_IT,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1556: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, uint8_t *, uint16_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t)"
                           HAL_UARTEx_ReceiveToIdle_IT))
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1945: Warning:  #144-D: a value of type "void *(*)(const char *, uint32_t, _Bool, void *, void (*)(void *))" cannot be used to initialize an entity of type "rs485_os_ret_t (*)(const char *, uint32_t, void *, void (*)(void *), void **)"
          .rtos_timer_create  = (void*(*)(const char*, uint32_t, bool, void*, void(*)(void*)))os_timer_create,
                                ^
".\..\SYSTEM\Src\system_adaption.c", line 2227: Error:  #525: a dependent statement may not be a declaration
      uart_led_indicator_config_t led_485_config = 
      ^
".\..\SYSTEM\Src\system_adaption.c", line 2232: Error:  #144: a value of type "led_handler_timebase_interface_t *" cannot be used to initialize an entity of type "uart_led_indicator_timebase_interface_t *"
          .p_timebase_interface = &led_handler_timebase_interface
                                  ^
".\..\SYSTEM\Src\system_adaption.c", line 2234: Error:  #20: identifier "led_485_config" is undefined
      if(UART_LED_INDICATOR_OK != uart_led_indicator_init(&led_485_config))
                                                           ^
".\..\SYSTEM\Src\system_adaption.c", line 97: Warning:  #177-D: variable "g_adc_range_default"  was declared but never referenced
  static adc_collect_set_range_t g_adc_range_default = ADC_COLLECT_RANGE_5V;
                                 ^
.\..\SYSTEM\Src\system_adaption.c: 13 warnings, 3 errors

[2025-07-31 23:10:02]	[info] incremental build: 2 source files changed
These source files will be recompiled
---
'd:\arrester_insulation_monitoring\04_Software\02_Busbar_Voltage_Online_Monitor\BSP\BSP_LED\handler\Src\uart_led_indicator.c': object (.o) file not exist
'd:\arrester_insulation_monitoring\04_Software\02_Busbar_Voltage_Online_Monitor\SYSTEM\Src\system_adaption.c': object (.o) file not exist

[2025-07-31 23:10:02]	compilation failed at : "d:\arrester_insulation_monitoring\04_Software\02_Busbar_Voltage_Online_Monitor\BSP\BSP_LED\handler\Src\uart_led_indicator.c", exit code: 1
command: 
  "C:\Keil_v5\ARM\ARMCC\bin\armcc.exe" -c --apcs=interwork -I. -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../BSP/BSP_AD7606/hal_driver/Src -I../BSP/BSP_AD7606/hal_driver/Inc -I../MCU_Peripherals_Drivers/Inc -I../MCU_Peripherals_Drivers/Src -I../Middlewares/linked_list -I../easylogger/inc -I../easylogger/src -I../easylogger/port -I../RTT/Config -I../RTT/RTT -I../BSP/BSP_LED/hal_driver/Inc -I../BSP/BSP_LED/hal_driver/Src -I../unit_test/Inc -I../unit_test/Src -I../BSP/BSP_Analog_Switch/hal_driver/Inc -I../BSP/BSP_Analog_Switch/hal_driver/Src -I../BSP/BSP_AD7606/handler/Inc -I../BSP/BSP_AD7606/handler/Src -I../BSP/BSP_Analog_Switch/handler/Inc -I../BSP/BSP_Analog_Switch/handler/Src -I../Middlewares/cm_backtrace -I../Middlewares/cm_backtrace/Languages/en-US -I../Middlewares/cm_backtrace/Languages/zh-CN -I../Tasks/Data_Process_Task/Inc -I../Tasks/Data_Process_Task/Src -I../Tasks/FFT_Task/Inc -I../Tasks/FFT_Task/Src -I../SYSTEM/Inc -I../SYSTEM/Src -I../Middlewares/Algorithm -I../Drivers/CMSIS/DSP/Include -I../Drivers/CMSIS/DSP/PrivateInclude -I../Drivers/CMSIS/DSP/Source/TransformFunctions -I../BSP/BSP_RTC/hal_driver/Inc -I../BSP/BSP_RTC/hal_driver/Src -I../BSP/BSP_RTC/handler/Inc -I../BSP/BSP_RTC/handler/Src -I../Tasks/Thunderstrike_Task/Inc -I../Tasks/Thunderstrike_Task/Src -I../Middlewares/RingBuff/Inc -I../Middlewares/RingBuff/Src -I../Middlewares/Systemview/Config -I../Middlewares/Systemview/Sample/FreeRTOSV10.4 -I../Middlewares/Systemview/SEGGER -I../Drivers/CMSIS/DSP/Source/FastMathFunctions -I../Middlewares/LPF_FIR_Algorithm/Src -I../Middlewares/LPF_FIR_Algorithm/Inc -I../Busbar_Voltage_Online_Monitor/Flash/handler/Inc -I../Busbar_Voltage_Online_Monitor/Flash/handler/Src -I../Busbar_Voltage_Online_Monitor/RS485/hal_driver/Inc -I../Busbar_Voltage_Online_Monitor/RS485/hal_driver/Src -I../Busbar_Voltage_Online_Monitor/RS485/handler/Inc -I../Busbar_Voltage_Online_Monitor/RS485/handler/Src -I../Busbar_Voltage_Online_Monitor/Uart_dev/Inc -I../Busbar_Voltage_Online_Monitor/Uart_dev/Src -I.cmsis/include -I../MDK-ARM/RTE/_Busbar_Voltage_Online_Monitor -I../Middlewares/OSAL/src -I../Middlewares/OSAL/inc -I../BSP/BSP_LED/handler/Inc -I../BSP/BSP_LED/handler/Src -D"USE_HAL_DRIVER" -D"STM32F411xE" -D"USE_FULL_ASSERT" -D"ARM_MATH_CM4" -D"__CC_ARM" -D"ARM_MATH_MATRIX_CHECK" -D"ARM_MATH_ROUNDING" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=2803,1,1035 -g -o .\build\Busbar_Voltage_Online_Monitor\.obj\__\BSP\BSP_LED\handler\Src\uart_led_indicator.o --no_depend_system_headers --depend .\build\Busbar_Voltage_Online_Monitor\.obj\__\BSP\BSP_LED\handler\Src\uart_led_indicator.d .\..\BSP\BSP_LED\handler\Src\uart_led_indicator.c
   at unify_builder.Program.Main(String[] args_)
---
".\..\BSP\BSP_LED\handler\Src\uart_led_indicator.c", line 83: Error:  #165: too few arguments in function call
          led_handler_ret_code_t ret = bsp_led_handler_control(p_data->p_led_handler, &control_param);
                                                                                                    ^
".\..\BSP\BSP_LED\handler\Src\uart_led_indicator.c", line 137: Error:  #165: too few arguments in function call
          led_handler_ret_code_t ret = bsp_led_handler_control(p_data->p_led_handler, &control_param);
                                                                                                    ^
.\..\BSP\BSP_LED\handler\Src\uart_led_indicator.c: 0 warnings, 2 errors

[2025-07-31 23:13:33]	[info] incremental build: 2 source files changed
These source files will be recompiled
---
'd:\arrester_insulation_monitoring\04_Software\02_Busbar_Voltage_Online_Monitor\BSP\BSP_LED\handler\Src\uart_led_indicator.c': object (.o) file not exist
'd:\arrester_insulation_monitoring\04_Software\02_Busbar_Voltage_Online_Monitor\SYSTEM\Src\system_adaption.c': object (.o) file not exist

[2025-07-31 23:13:33]	compilation failed at : "d:\arrester_insulation_monitoring\04_Software\02_Busbar_Voltage_Online_Monitor\SYSTEM\Src\system_adaption.c", exit code: 1
command: 
  "C:\Keil_v5\ARM\ARMCC\bin\armcc.exe" -c --apcs=interwork -I. -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../BSP/BSP_AD7606/hal_driver/Src -I../BSP/BSP_AD7606/hal_driver/Inc -I../MCU_Peripherals_Drivers/Inc -I../MCU_Peripherals_Drivers/Src -I../Middlewares/linked_list -I../easylogger/inc -I../easylogger/src -I../easylogger/port -I../RTT/Config -I../RTT/RTT -I../BSP/BSP_LED/hal_driver/Inc -I../BSP/BSP_LED/hal_driver/Src -I../unit_test/Inc -I../unit_test/Src -I../BSP/BSP_Analog_Switch/hal_driver/Inc -I../BSP/BSP_Analog_Switch/hal_driver/Src -I../BSP/BSP_AD7606/handler/Inc -I../BSP/BSP_AD7606/handler/Src -I../BSP/BSP_Analog_Switch/handler/Inc -I../BSP/BSP_Analog_Switch/handler/Src -I../Middlewares/cm_backtrace -I../Middlewares/cm_backtrace/Languages/en-US -I../Middlewares/cm_backtrace/Languages/zh-CN -I../Tasks/Data_Process_Task/Inc -I../Tasks/Data_Process_Task/Src -I../Tasks/FFT_Task/Inc -I../Tasks/FFT_Task/Src -I../SYSTEM/Inc -I../SYSTEM/Src -I../Middlewares/Algorithm -I../Drivers/CMSIS/DSP/Include -I../Drivers/CMSIS/DSP/PrivateInclude -I../Drivers/CMSIS/DSP/Source/TransformFunctions -I../BSP/BSP_RTC/hal_driver/Inc -I../BSP/BSP_RTC/hal_driver/Src -I../BSP/BSP_RTC/handler/Inc -I../BSP/BSP_RTC/handler/Src -I../Tasks/Thunderstrike_Task/Inc -I../Tasks/Thunderstrike_Task/Src -I../Middlewares/RingBuff/Inc -I../Middlewares/RingBuff/Src -I../Middlewares/Systemview/Config -I../Middlewares/Systemview/Sample/FreeRTOSV10.4 -I../Middlewares/Systemview/SEGGER -I../Drivers/CMSIS/DSP/Source/FastMathFunctions -I../Middlewares/LPF_FIR_Algorithm/Src -I../Middlewares/LPF_FIR_Algorithm/Inc -I../Busbar_Voltage_Online_Monitor/Flash/handler/Inc -I../Busbar_Voltage_Online_Monitor/Flash/handler/Src -I../Busbar_Voltage_Online_Monitor/RS485/hal_driver/Inc -I../Busbar_Voltage_Online_Monitor/RS485/hal_driver/Src -I../Busbar_Voltage_Online_Monitor/RS485/handler/Inc -I../Busbar_Voltage_Online_Monitor/RS485/handler/Src -I../Busbar_Voltage_Online_Monitor/Uart_dev/Inc -I../Busbar_Voltage_Online_Monitor/Uart_dev/Src -I.cmsis/include -I../MDK-ARM/RTE/_Busbar_Voltage_Online_Monitor -I../Middlewares/OSAL/src -I../Middlewares/OSAL/inc -I../BSP/BSP_LED/handler/Inc -I../BSP/BSP_LED/handler/Src -D"USE_HAL_DRIVER" -D"STM32F411xE" -D"USE_FULL_ASSERT" -D"ARM_MATH_CM4" -D"__CC_ARM" -D"ARM_MATH_MATRIX_CHECK" -D"ARM_MATH_ROUNDING" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=2803,1,1035 -g -o .\build\Busbar_Voltage_Online_Monitor\.obj\__\SYSTEM\Src\system_adaption.o --no_depend_system_headers --depend .\build\Busbar_Voltage_Online_Monitor\.obj\__\SYSTEM\Src\system_adaption.d .\..\SYSTEM\Src\system_adaption.c
   at unify_builder.Program.Main(String[] args_)
---
"../MCU_Peripherals_Drivers/Inc/mcu_spi_driver.h", line 43: Warning:  #47-D: incompatible redefinition of macro "MCU_SPI_DRIVER_DEBUG"  (declared at line 94 of "../SYSTEM/Inc/system_config.h")
  #define MCU_SPI_DRIVER_DEBUG(...)
          ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 42: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_DEBUG"  (declared at line 88 of "../SYSTEM/Inc/system_config.h")
      #define AD7606_DRIVER_DEBUG(...) printf(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 53: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_LOG_IRQ"  (declared at line 38)
      #define AD7606_DRIVER_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/handler/Inc/bsp_adc_collect_xxx_handler.h", line 58: Warning:  #47-D: incompatible redefinition of macro "ADC_COLLECT_LOG_IRQ"  (declared at line 39)
  #define ADC_COLLECT_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
          ^
".\..\SYSTEM\Src\system_adaption.c", line 756: Warning:  #550-D: variable "fft_calcu_adc_arg"  was set but never used
      static fft_calcu_adc_arg_t fft_calcu_adc_arg;
                                 ^
".\..\SYSTEM\Src\system_adaption.c", line 897: Warning:  #550-D: variable "ac_phase_data"  was set but never used
      static float ac_phase_data = 0.0f;
                   ^
".\..\SYSTEM\Src\system_adaption.c", line 1552: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, const uint8_t *, uint16_t, uint32_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t, uint32_t)"
                           HAL_UART_Transmit,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1553: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, const uint8_t *, uint16_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t)"
                           HAL_UART_Transmit_IT,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1554: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, uint8_t *, uint16_t, uint32_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t, uint32_t)"
                           HAL_UART_Receive,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1555: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, uint8_t *, uint16_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t)"
                           HAL_UART_Receive_IT,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1556: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, uint8_t *, uint16_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t)"
                           HAL_UARTEx_ReceiveToIdle_IT))
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1945: Warning:  #144-D: a value of type "void *(*)(const char *, uint32_t, _Bool, void *, void (*)(void *))" cannot be used to initialize an entity of type "rs485_os_ret_t (*)(const char *, uint32_t, void *, void (*)(void *), void **)"
          .rtos_timer_create  = (void*(*)(const char*, uint32_t, bool, void*, void(*)(void*)))os_timer_create,
                                ^
".\..\SYSTEM\Src\system_adaption.c", line 2227: Error:  #525: a dependent statement may not be a declaration
      uart_led_indicator_config_t led_485_config = 
      ^
".\..\SYSTEM\Src\system_adaption.c", line 2232: Error:  #144: a value of type "led_handler_timebase_interface_t *" cannot be used to initialize an entity of type "uart_led_indicator_timebase_interface_t *"
          .p_timebase_interface = &led_handler_timebase_interface
                                  ^
".\..\SYSTEM\Src\system_adaption.c", line 2234: Error:  #20: identifier "led_485_config" is undefined
      if(UART_LED_INDICATOR_OK != uart_led_indicator_init(&led_485_config))
                                                           ^
".\..\SYSTEM\Src\system_adaption.c", line 97: Warning:  #177-D: variable "g_adc_range_default"  was declared but never referenced
  static adc_collect_set_range_t g_adc_range_default = ADC_COLLECT_RANGE_5V;
                                 ^
.\..\SYSTEM\Src\system_adaption.c: 13 warnings, 3 errors

[2025-07-31 23:18:25]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'd:\arrester_insulation_monitoring\04_Software\02_Busbar_Voltage_Online_Monitor\SYSTEM\Src\system_adaption.c': object (.o) file not exist

[2025-07-31 23:18:25]	compilation failed at : "d:\arrester_insulation_monitoring\04_Software\02_Busbar_Voltage_Online_Monitor\SYSTEM\Src\system_adaption.c", exit code: 1
command: 
  "C:\Keil_v5\ARM\ARMCC\bin\armcc.exe" -c --apcs=interwork -I. -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../BSP/BSP_AD7606/hal_driver/Src -I../BSP/BSP_AD7606/hal_driver/Inc -I../MCU_Peripherals_Drivers/Inc -I../MCU_Peripherals_Drivers/Src -I../Middlewares/linked_list -I../easylogger/inc -I../easylogger/src -I../easylogger/port -I../RTT/Config -I../RTT/RTT -I../BSP/BSP_LED/hal_driver/Inc -I../BSP/BSP_LED/hal_driver/Src -I../unit_test/Inc -I../unit_test/Src -I../BSP/BSP_Analog_Switch/hal_driver/Inc -I../BSP/BSP_Analog_Switch/hal_driver/Src -I../BSP/BSP_AD7606/handler/Inc -I../BSP/BSP_AD7606/handler/Src -I../BSP/BSP_Analog_Switch/handler/Inc -I../BSP/BSP_Analog_Switch/handler/Src -I../Middlewares/cm_backtrace -I../Middlewares/cm_backtrace/Languages/en-US -I../Middlewares/cm_backtrace/Languages/zh-CN -I../Tasks/Data_Process_Task/Inc -I../Tasks/Data_Process_Task/Src -I../Tasks/FFT_Task/Inc -I../Tasks/FFT_Task/Src -I../SYSTEM/Inc -I../SYSTEM/Src -I../Middlewares/Algorithm -I../Drivers/CMSIS/DSP/Include -I../Drivers/CMSIS/DSP/PrivateInclude -I../Drivers/CMSIS/DSP/Source/TransformFunctions -I../BSP/BSP_RTC/hal_driver/Inc -I../BSP/BSP_RTC/hal_driver/Src -I../BSP/BSP_RTC/handler/Inc -I../BSP/BSP_RTC/handler/Src -I../Tasks/Thunderstrike_Task/Inc -I../Tasks/Thunderstrike_Task/Src -I../Middlewares/RingBuff/Inc -I../Middlewares/RingBuff/Src -I../Middlewares/Systemview/Config -I../Middlewares/Systemview/Sample/FreeRTOSV10.4 -I../Middlewares/Systemview/SEGGER -I../Drivers/CMSIS/DSP/Source/FastMathFunctions -I../Middlewares/LPF_FIR_Algorithm/Src -I../Middlewares/LPF_FIR_Algorithm/Inc -I../Busbar_Voltage_Online_Monitor/Flash/handler/Inc -I../Busbar_Voltage_Online_Monitor/Flash/handler/Src -I../Busbar_Voltage_Online_Monitor/RS485/hal_driver/Inc -I../Busbar_Voltage_Online_Monitor/RS485/hal_driver/Src -I../Busbar_Voltage_Online_Monitor/RS485/handler/Inc -I../Busbar_Voltage_Online_Monitor/RS485/handler/Src -I../Busbar_Voltage_Online_Monitor/Uart_dev/Inc -I../Busbar_Voltage_Online_Monitor/Uart_dev/Src -I.cmsis/include -I../MDK-ARM/RTE/_Busbar_Voltage_Online_Monitor -I../Middlewares/OSAL/src -I../Middlewares/OSAL/inc -I../BSP/BSP_LED/handler/Inc -I../BSP/BSP_LED/handler/Src -D"USE_HAL_DRIVER" -D"STM32F411xE" -D"USE_FULL_ASSERT" -D"ARM_MATH_CM4" -D"__CC_ARM" -D"ARM_MATH_MATRIX_CHECK" -D"ARM_MATH_ROUNDING" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=2803,1,1035 -g -o .\build\Busbar_Voltage_Online_Monitor\.obj\__\SYSTEM\Src\system_adaption.o --no_depend_system_headers --depend .\build\Busbar_Voltage_Online_Monitor\.obj\__\SYSTEM\Src\system_adaption.d .\..\SYSTEM\Src\system_adaption.c
   at unify_builder.Program.Main(String[] args_)
---
"../MCU_Peripherals_Drivers/Inc/mcu_spi_driver.h", line 43: Warning:  #47-D: incompatible redefinition of macro "MCU_SPI_DRIVER_DEBUG"  (declared at line 94 of "../SYSTEM/Inc/system_config.h")
  #define MCU_SPI_DRIVER_DEBUG(...)
          ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 42: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_DEBUG"  (declared at line 88 of "../SYSTEM/Inc/system_config.h")
      #define AD7606_DRIVER_DEBUG(...) printf(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 53: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_LOG_IRQ"  (declared at line 38)
      #define AD7606_DRIVER_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/handler/Inc/bsp_adc_collect_xxx_handler.h", line 58: Warning:  #47-D: incompatible redefinition of macro "ADC_COLLECT_LOG_IRQ"  (declared at line 39)
  #define ADC_COLLECT_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
          ^
".\..\SYSTEM\Src\system_adaption.c", line 756: Warning:  #550-D: variable "fft_calcu_adc_arg"  was set but never used
      static fft_calcu_adc_arg_t fft_calcu_adc_arg;
                                 ^
".\..\SYSTEM\Src\system_adaption.c", line 897: Warning:  #550-D: variable "ac_phase_data"  was set but never used
      static float ac_phase_data = 0.0f;
                   ^
".\..\SYSTEM\Src\system_adaption.c", line 1552: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, const uint8_t *, uint16_t, uint32_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t, uint32_t)"
                           HAL_UART_Transmit,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1553: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, const uint8_t *, uint16_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t)"
                           HAL_UART_Transmit_IT,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1554: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, uint8_t *, uint16_t, uint32_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t, uint32_t)"
                           HAL_UART_Receive,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1555: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, uint8_t *, uint16_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t)"
                           HAL_UART_Receive_IT,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1556: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, uint8_t *, uint16_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t)"
                           HAL_UARTEx_ReceiveToIdle_IT))
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1945: Warning:  #144-D: a value of type "void *(*)(const char *, uint32_t, _Bool, void *, void (*)(void *))" cannot be used to initialize an entity of type "rs485_os_ret_t (*)(const char *, uint32_t, void *, void (*)(void *), void **)"
          .rtos_timer_create  = (void*(*)(const char*, uint32_t, bool, void*, void(*)(void*)))os_timer_create,
                                ^
".\..\SYSTEM\Src\system_adaption.c", line 2227: Error:  #525: a dependent statement may not be a declaration
      uart_led_indicator_config_t led_485_config = 
      ^
".\..\SYSTEM\Src\system_adaption.c", line 2232: Error:  #144: a value of type "led_handler_timebase_interface_t *" cannot be used to initialize an entity of type "uart_led_indicator_timebase_interface_t *"
          .p_timebase_interface = &led_handler_timebase_interface
                                  ^
".\..\SYSTEM\Src\system_adaption.c", line 2234: Error:  #20: identifier "led_485_config" is undefined
      if(UART_LED_INDICATOR_OK != uart_led_indicator_init(&led_485_config))
                                                           ^
".\..\SYSTEM\Src\system_adaption.c", line 97: Warning:  #177-D: variable "g_adc_range_default"  was declared but never referenced
  static adc_collect_set_range_t g_adc_range_default = ADC_COLLECT_RANGE_5V;
                                 ^
.\..\SYSTEM\Src\system_adaption.c: 13 warnings, 3 errors

[2025-07-31 23:21:24]	[info] incremental build: 1 source files changed
These source files will be recompiled
---
'd:\arrester_insulation_monitoring\04_Software\02_Busbar_Voltage_Online_Monitor\SYSTEM\Src\system_adaption.c': object (.o) file not exist

[2025-07-31 23:21:24]	compilation failed at : "d:\arrester_insulation_monitoring\04_Software\02_Busbar_Voltage_Online_Monitor\SYSTEM\Src\system_adaption.c", exit code: 1
command: 
  "C:\Keil_v5\ARM\ARMCC\bin\armcc.exe" -c --apcs=interwork -I. -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Middlewares/Third_Party/FreeRTOS/Source/include -I../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2 -I../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../BSP/BSP_AD7606/hal_driver/Src -I../BSP/BSP_AD7606/hal_driver/Inc -I../MCU_Peripherals_Drivers/Inc -I../MCU_Peripherals_Drivers/Src -I../Middlewares/linked_list -I../easylogger/inc -I../easylogger/src -I../easylogger/port -I../RTT/Config -I../RTT/RTT -I../BSP/BSP_LED/hal_driver/Inc -I../BSP/BSP_LED/hal_driver/Src -I../unit_test/Inc -I../unit_test/Src -I../BSP/BSP_Analog_Switch/hal_driver/Inc -I../BSP/BSP_Analog_Switch/hal_driver/Src -I../BSP/BSP_AD7606/handler/Inc -I../BSP/BSP_AD7606/handler/Src -I../BSP/BSP_Analog_Switch/handler/Inc -I../BSP/BSP_Analog_Switch/handler/Src -I../Middlewares/cm_backtrace -I../Middlewares/cm_backtrace/Languages/en-US -I../Middlewares/cm_backtrace/Languages/zh-CN -I../Tasks/Data_Process_Task/Inc -I../Tasks/Data_Process_Task/Src -I../Tasks/FFT_Task/Inc -I../Tasks/FFT_Task/Src -I../SYSTEM/Inc -I../SYSTEM/Src -I../Middlewares/Algorithm -I../Drivers/CMSIS/DSP/Include -I../Drivers/CMSIS/DSP/PrivateInclude -I../Drivers/CMSIS/DSP/Source/TransformFunctions -I../BSP/BSP_RTC/hal_driver/Inc -I../BSP/BSP_RTC/hal_driver/Src -I../BSP/BSP_RTC/handler/Inc -I../BSP/BSP_RTC/handler/Src -I../Tasks/Thunderstrike_Task/Inc -I../Tasks/Thunderstrike_Task/Src -I../Middlewares/RingBuff/Inc -I../Middlewares/RingBuff/Src -I../Middlewares/Systemview/Config -I../Middlewares/Systemview/Sample/FreeRTOSV10.4 -I../Middlewares/Systemview/SEGGER -I../Drivers/CMSIS/DSP/Source/FastMathFunctions -I../Middlewares/LPF_FIR_Algorithm/Src -I../Middlewares/LPF_FIR_Algorithm/Inc -I../Busbar_Voltage_Online_Monitor/Flash/handler/Inc -I../Busbar_Voltage_Online_Monitor/Flash/handler/Src -I../Busbar_Voltage_Online_Monitor/RS485/hal_driver/Inc -I../Busbar_Voltage_Online_Monitor/RS485/hal_driver/Src -I../Busbar_Voltage_Online_Monitor/RS485/handler/Inc -I../Busbar_Voltage_Online_Monitor/RS485/handler/Src -I../Busbar_Voltage_Online_Monitor/Uart_dev/Inc -I../Busbar_Voltage_Online_Monitor/Uart_dev/Src -I.cmsis/include -I../MDK-ARM/RTE/_Busbar_Voltage_Online_Monitor -I../Middlewares/OSAL/src -I../Middlewares/OSAL/inc -I../BSP/BSP_LED/handler/Inc -I../BSP/BSP_LED/handler/Src -D"USE_HAL_DRIVER" -D"STM32F411xE" -D"USE_FULL_ASSERT" -D"ARM_MATH_CM4" -D"__CC_ARM" -D"ARM_MATH_MATRIX_CHECK" -D"ARM_MATH_ROUNDING" --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O0 --split_sections --diag_suppress=2803,1,1035 -g -o .\build\Busbar_Voltage_Online_Monitor\.obj\__\SYSTEM\Src\system_adaption.o --no_depend_system_headers --depend .\build\Busbar_Voltage_Online_Monitor\.obj\__\SYSTEM\Src\system_adaption.d .\..\SYSTEM\Src\system_adaption.c
   at unify_builder.Program.Main(String[] args_)
---
"../MCU_Peripherals_Drivers/Inc/mcu_spi_driver.h", line 43: Warning:  #47-D: incompatible redefinition of macro "MCU_SPI_DRIVER_DEBUG"  (declared at line 94 of "../SYSTEM/Inc/system_config.h")
  #define MCU_SPI_DRIVER_DEBUG(...)
          ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 42: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_DEBUG"  (declared at line 88 of "../SYSTEM/Inc/system_config.h")
      #define AD7606_DRIVER_DEBUG(...) printf(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/hal_driver/Inc/ad7606_driver.h", line 53: Warning:  #47-D: incompatible redefinition of macro "AD7606_DRIVER_LOG_IRQ"  (declared at line 38)
      #define AD7606_DRIVER_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
              ^
"../BSP/BSP_AD7606/handler/Inc/bsp_adc_collect_xxx_handler.h", line 58: Warning:  #47-D: incompatible redefinition of macro "ADC_COLLECT_LOG_IRQ"  (declared at line 39)
  #define ADC_COLLECT_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
          ^
".\..\SYSTEM\Src\system_adaption.c", line 756: Warning:  #550-D: variable "fft_calcu_adc_arg"  was set but never used
      static fft_calcu_adc_arg_t fft_calcu_adc_arg;
                                 ^
".\..\SYSTEM\Src\system_adaption.c", line 897: Warning:  #550-D: variable "ac_phase_data"  was set but never used
      static float ac_phase_data = 0.0f;
                   ^
".\..\SYSTEM\Src\system_adaption.c", line 1552: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, const uint8_t *, uint16_t, uint32_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t, uint32_t)"
                           HAL_UART_Transmit,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1553: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, const uint8_t *, uint16_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t)"
                           HAL_UART_Transmit_IT,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1554: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, uint8_t *, uint16_t, uint32_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t, uint32_t)"
                           HAL_UART_Receive,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1555: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, uint8_t *, uint16_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t)"
                           HAL_UART_Receive_IT,
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1556: Warning:  #167-D: argument of type "HAL_StatusTypeDef (*)(UART_HandleTypeDef *, uint8_t *, uint16_t)" is incompatible with parameter of type "int8_t (*)(void *, uint8_t *, uint16_t)"
                           HAL_UARTEx_ReceiveToIdle_IT))
                           ^
".\..\SYSTEM\Src\system_adaption.c", line 1945: Warning:  #144-D: a value of type "void *(*)(const char *, uint32_t, _Bool, void *, void (*)(void *))" cannot be used to initialize an entity of type "rs485_os_ret_t (*)(const char *, uint32_t, void *, void (*)(void *), void **)"
          .rtos_timer_create  = (void*(*)(const char*, uint32_t, bool, void*, void(*)(void*)))os_timer_create,
                                ^
".\..\SYSTEM\Src\system_adaption.c", line 2227: Error:  #525: a dependent statement may not be a declaration
      uart_led_indicator_config_t led_485_config = 
      ^
".\..\SYSTEM\Src\system_adaption.c", line 2232: Error:  #144: a value of type "led_handler_timebase_interface_t *" cannot be used to initialize an entity of type "uart_led_indicator_timebase_interface_t *"
          .p_timebase_interface = &led_handler_timebase_interface
                                  ^
".\..\SYSTEM\Src\system_adaption.c", line 2234: Error:  #20: identifier "led_485_config" is undefined
      if(UART_LED_INDICATOR_OK != uart_led_indicator_init(&led_485_config))
                                                           ^
".\..\SYSTEM\Src\system_adaption.c", line 97: Warning:  #177-D: variable "g_adc_range_default"  was declared but never referenced
  static adc_collect_set_range_t g_adc_range_default = ADC_COLLECT_RANGE_5V;
                                 ^
.\..\SYSTEM\Src\system_adaption.c: 13 warnings, 3 errors

