/******************************************************************************
 * @file uart_led_indicator.c
 * @brief 串口数据接收LED指示器实现文件
 * <AUTHOR>
 * @version 1.0
 * @date 2024-11-01
 *
 * @copyright Copyright (c) 2024
 *
 * Processing flow:
 * 1. 串口接收中断触发 -> uart_led_indicator_on_data_received()
 * 2. 检查当前状态，如果是空闲状态则启动LED
 * 3. 更新最后接收时间戳
 * 4. 周期性任务检查超时，超时后关闭LED
 *
 * @par dependencies
 * - bsp_led_handler.h
 * - uart_led_indicator.h
 *
 * @note 1 tab == 4 spaces!
 *
 *****************************************************************************/

//******************************** Includes *********************************//
#include "uart_led_indicator.h"
#include "elog.h"
#include <string.h>
//******************************** Includes *********************************//

//******************************** Defines **********************************//
#define UART_LED_INDICATOR_LOG_TAG    "UART_LED"

#define UART_LED_INDICATOR_LOG_ERROR(...)   log_e(__VA_ARGS__)
#define UART_LED_INDICATOR_LOG_WARN(...)    log_w(__VA_ARGS__)
#define UART_LED_INDICATOR_LOG_INFO(...)    log_i(__VA_ARGS__)
#define UART_LED_INDICATOR_LOG_DEBUG(...)   log_d(__VA_ARGS__)
//******************************** Defines **********************************//

//******************************** Types ************************************//
/* 私有数据结构体 */
typedef struct
{
    bool                                     is_initialized;        /* 初始化标志 */
    bsp_led_handler_t                        *p_led_handler;        /* LED处理器指针 */
    led_which_t                              led_which;             /* LED编号 */
    uint32_t                                 timeout_ms;            /* 超时时间 */
    uart_led_indicator_timebase_interface_t  *p_timebase_interface; /* 时基接口 */
    
    uart_led_indicator_state_t               current_state;         /* 当前状态 */
    uint32_t                                 last_receive_time;     /* 最后接收时间 */
    bool                                     led_is_on;             /* LED是否已亮起 */
}uart_led_indicator_private_data_t;
//******************************** Types ************************************//

//******************************** Variables ********************************//
/* 私有数据实例 */
static uart_led_indicator_private_data_t g_uart_led_indicator_data = {0};
//******************************** Variables ********************************//

//******************************** Functions ********************************//
/**
 * @brief 启动LED指示
 * 
 * @return uart_led_indicator_ret_code_t 返回码
 */
static uart_led_indicator_ret_code_t _start_led_indication(void)
{
    uart_led_indicator_private_data_t *p_data = &g_uart_led_indicator_data;
    
    if (!p_data->led_is_on) {
        /* LED未亮起，启动定时亮起模式 */
        led_control_param_t control_param = {
            .led_which = p_data->led_which,
            .mode = LED_MODE_TIMER_ON,
            .timer_duration_ms = p_data->timeout_ms,
            .cycle_time_ms = 0,
            .blink_count = 0,
            .proportion = LED_BLINK_RATIO_1_1,
            .brightness = 100,
            .breath_period_ms = 0
        };
        
        led_handler_ret_code_t ret = bsp_led_handler_control(p_data->p_led_handler, &control_param, 1);
        if (ret == LED_HANDLER_OK) {
            p_data->led_is_on = true;
            UART_LED_INDICATOR_LOG_DEBUG("LED indication started, timeout: %d ms", p_data->timeout_ms);
            return UART_LED_INDICATOR_OK;
        } else {
            UART_LED_INDICATOR_LOG_ERROR("Failed to start LED indication, ret: %d", ret);
            return UART_LED_INDICATOR_ERROR;
        }
    } else {
        /* LED已亮起，重新启动定时器（刷新超时时间） */
        led_control_param_t control_param = {
            .led_which = p_data->led_which,
            .mode = LED_MODE_TIMER_ON,
            .timer_duration_ms = p_data->timeout_ms,
            .cycle_time_ms = 0,
            .blink_count = 0,
            .proportion = LED_BLINK_RATIO_1_1,
            .brightness = 100,
            .breath_period_ms = 0
        };
        
        led_handler_ret_code_t ret = bsp_led_handler_control(p_data->p_led_handler, &control_param, 1);
        if (ret == LED_HANDLER_OK) {
            UART_LED_INDICATOR_LOG_DEBUG("LED indication refreshed");
            return UART_LED_INDICATOR_OK;
        } else {
            UART_LED_INDICATOR_LOG_ERROR("Failed to refresh LED indication, ret: %d", ret);
            return UART_LED_INDICATOR_ERROR;
        }
    }
}

/**
 * @brief 停止LED指示
 * 
 * @return uart_led_indicator_ret_code_t 返回码
 */
static uart_led_indicator_ret_code_t _stop_led_indication(void)
{
    uart_led_indicator_private_data_t *p_data = &g_uart_led_indicator_data;
    
    if (p_data->led_is_on) {
        led_control_param_t control_param = {
            .led_which = p_data->led_which,
            .mode = LED_MODE_OFF,
            .timer_duration_ms = 0,
            .cycle_time_ms = 0,
            .blink_count = 0,
            .proportion = LED_BLINK_RATIO_1_1,
            .brightness = 0,
            .breath_period_ms = 0
        };
        
        led_handler_ret_code_t ret = bsp_led_handler_control(p_data->p_led_handler, &control_param, 1);
        if (ret == LED_HANDLER_OK) {
            p_data->led_is_on = false;
            UART_LED_INDICATOR_LOG_DEBUG("LED indication stopped");
            return UART_LED_INDICATOR_OK;
        } else {
            UART_LED_INDICATOR_LOG_ERROR("Failed to stop LED indication, ret: %d", ret);
            return UART_LED_INDICATOR_ERROR;
        }
    }
    
    return UART_LED_INDICATOR_OK;
}

/**
 * @brief 初始化串口LED指示器
 * 
 * @param p_config 配置参数指针
 * @return uart_led_indicator_ret_code_t 返回码
 */
uart_led_indicator_ret_code_t uart_led_indicator_init(uart_led_indicator_config_t *p_config)
{
    uart_led_indicator_private_data_t *p_data = &g_uart_led_indicator_data;
    
    /* 参数检查 */
    if (p_config == NULL || 
        p_config->p_led_handler == NULL || 
        p_config->p_timebase_interface == NULL ||
        p_config->p_timebase_interface->pfget_timetick_ms == NULL) {
        UART_LED_INDICATOR_LOG_ERROR("Invalid parameters");
        return UART_LED_INDICATOR_ERROR_PARAM;
    }
    
    if (p_data->is_initialized) {
        UART_LED_INDICATOR_LOG_WARN("Already initialized");
        return UART_LED_INDICATOR_ERROR_ALREADY_INIT;
    }
    
    /* 超时时间范围检查 */
    if (p_config->timeout_ms < UART_LED_INDICATOR_MIN_TIMEOUT_MS || 
        p_config->timeout_ms > UART_LED_INDICATOR_MAX_TIMEOUT_MS) {
        UART_LED_INDICATOR_LOG_ERROR("Invalid timeout: %d ms (range: %d-%d)", 
                                     p_config->timeout_ms,
                                     UART_LED_INDICATOR_MIN_TIMEOUT_MS,
                                     UART_LED_INDICATOR_MAX_TIMEOUT_MS);
        return UART_LED_INDICATOR_ERROR_PARAM;
    }
    
    /* 初始化私有数据 */
    p_data->p_led_handler = p_config->p_led_handler;
    p_data->led_which = p_config->led_which;
    p_data->timeout_ms = p_config->timeout_ms;
    p_data->p_timebase_interface = p_config->p_timebase_interface;
    p_data->current_state = UART_LED_INDICATOR_STATE_IDLE;
    p_data->last_receive_time = 0;
    p_data->led_is_on = false;
    p_data->is_initialized = true;
    
    UART_LED_INDICATOR_LOG_INFO("UART LED indicator initialized, LED: %d, timeout: %d ms", 
                                p_config->led_which, p_config->timeout_ms);
    
    return UART_LED_INDICATOR_OK;
}

/**
 * @brief 反初始化串口LED指示器
 * 
 * @return uart_led_indicator_ret_code_t 返回码
 */
uart_led_indicator_ret_code_t uart_led_indicator_deinit(void)
{
    uart_led_indicator_private_data_t *p_data = &g_uart_led_indicator_data;
    
    if (!p_data->is_initialized) {
        return UART_LED_INDICATOR_ERROR_NOT_INIT;
    }
    
    /* 强制关闭LED */
    _stop_led_indication();
    
    /* 清零私有数据 */
    memset(p_data, 0, sizeof(uart_led_indicator_private_data_t));
    
    UART_LED_INDICATOR_LOG_INFO("UART LED indicator deinitialized");

    return UART_LED_INDICATOR_OK;
}

/**
 * @brief 数据接收事件处理（在串口接收中断中调用）
 *
 * @return uart_led_indicator_ret_code_t 返回码
 */
uart_led_indicator_ret_code_t uart_led_indicator_on_data_received(void)
{
    uart_led_indicator_private_data_t *p_data = &g_uart_led_indicator_data;

    if (!p_data->is_initialized) {
        return UART_LED_INDICATOR_ERROR_NOT_INIT;
    }

    /* 获取当前时间 */
    uint32_t current_time = p_data->p_timebase_interface->pfget_timetick_ms();
    p_data->last_receive_time = current_time;

    /* 状态转换 */
    if (p_data->current_state == UART_LED_INDICATOR_STATE_IDLE) {
        /* 从空闲状态转换到接收状态 */
        p_data->current_state = UART_LED_INDICATOR_STATE_RECEIVING;
        UART_LED_INDICATOR_LOG_DEBUG("State: IDLE -> RECEIVING");
    }

    /* 启动或刷新LED指示 */
    return _start_led_indication();
}

/**
 * @brief 周期性任务处理（需要在主循环或定时器中调用）
 *
 * @return uart_led_indicator_ret_code_t 返回码
 */
uart_led_indicator_ret_code_t uart_led_indicator_task(void)
{
    uart_led_indicator_private_data_t *p_data = &g_uart_led_indicator_data;

    if (!p_data->is_initialized) {
        return UART_LED_INDICATOR_ERROR_NOT_INIT;
    }

    /* 只在接收状态下检查超时 */
    if (p_data->current_state == UART_LED_INDICATOR_STATE_RECEIVING) {
        uint32_t current_time = p_data->p_timebase_interface->pfget_timetick_ms();
        uint32_t elapsed_time = current_time - p_data->last_receive_time;

        /* 检查是否超时 */
        if (elapsed_time >= p_data->timeout_ms) {
            /* 超时，转换到空闲状态 */
            p_data->current_state = UART_LED_INDICATOR_STATE_IDLE;
            p_data->led_is_on = false;  /* LED会由定时器自动关闭 */
            UART_LED_INDICATOR_LOG_DEBUG("State: RECEIVING -> IDLE (timeout)");
        }
    }

    return UART_LED_INDICATOR_OK;
}

/**
 * @brief 获取当前状态
 *
 * @return uart_led_indicator_state_t 当前状态
 */
uart_led_indicator_state_t uart_led_indicator_get_state(void)
{
    uart_led_indicator_private_data_t *p_data = &g_uart_led_indicator_data;

    if (!p_data->is_initialized) {
        return UART_LED_INDICATOR_STATE_IDLE;
    }

    return p_data->current_state;
}

/**
 * @brief 强制关闭LED指示
 *
 * @return uart_led_indicator_ret_code_t 返回码
 */
uart_led_indicator_ret_code_t uart_led_indicator_force_off(void)
{
    uart_led_indicator_private_data_t *p_data = &g_uart_led_indicator_data;

    if (!p_data->is_initialized) {
        return UART_LED_INDICATOR_ERROR_NOT_INIT;
    }

    /* 强制转换到空闲状态 */
    p_data->current_state = UART_LED_INDICATOR_STATE_IDLE;

    /* 停止LED指示 */
    uart_led_indicator_ret_code_t ret = _stop_led_indication();

    UART_LED_INDICATOR_LOG_DEBUG("LED indication forced off");

    return ret;
}
