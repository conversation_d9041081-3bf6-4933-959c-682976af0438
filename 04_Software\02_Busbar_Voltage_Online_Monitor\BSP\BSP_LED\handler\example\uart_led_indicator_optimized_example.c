/******************************************************************************
 * @file uart_led_indicator_optimized_example.c
 * @brief 优化后的串口LED指示器使用示例
 * <AUTHOR>
 * @version 2.0
 * @date 2024-11-01
 *
 * @copyright Copyright (c) 2024
 *
 * 本示例展示了两种使用模式：
 * 1. 事件驱动模式（推荐）- 无需周期性调用task函数
 * 2. 兼容模式 - 保持原有的轮询方式
 *
 * @note 1 tab == 4 spaces!
 *
 *****************************************************************************/

//******************************** Includes *********************************//
#include "uart_led_indicator.h"
#include "bsp_led_handler.h"
#include "system_adaption.h"
//******************************** Includes *********************************//

//******************************** Variables ********************************//
/* LED处理器实例 */
extern bsp_led_handler_t g_led_handler;

/* 时基接口实例 */
extern uart_led_indicator_timebase_interface_t led_handler_timebase_interface;

/* 串口LED指示器配置 */
static uart_led_indicator_config_t g_uart_led_config;

/* 用户数据示例 */
static uint32_t g_rx_data_count = 0;
//******************************** Variables ********************************//

//******************************** Functions ********************************//

/**
 * @brief 状态变化回调函数示例
 * 
 * @param new_state 新状态
 * @param p_user_data 用户数据指针
 */
void uart_led_state_callback(uart_led_indicator_state_t new_state, void *p_user_data)
{
    uint32_t *p_count = (uint32_t*)p_user_data;
    
    switch (new_state) {
        case UART_LED_INDICATOR_STATE_IDLE:
            printf("UART LED: Communication stopped, total received: %lu bytes\n", *p_count);
            break;
            
        case UART_LED_INDICATOR_STATE_RECEIVING:
            printf("UART LED: Communication started\n");
            break;
            
        default:
            break;
    }
}

/**
 * @brief 事件驱动模式初始化示例（推荐方式）
 * 
 * @return int 0-成功，其他-失败
 */
int uart_led_indicator_event_driven_init_example(void)
{
    /* 配置事件驱动模式 */
    g_uart_led_config.p_led_handler = &g_led_handler;
    g_uart_led_config.led_which = LED_485;
    g_uart_led_config.timeout_ms = 100;  /* 100ms超时 */
    g_uart_led_config.p_timebase_interface = &led_handler_timebase_interface;
    g_uart_led_config.state_callback = uart_led_state_callback;  /* 提供回调函数启用事件驱动 */
    g_uart_led_config.p_user_data = &g_rx_data_count;           /* 用户数据 */
    
    /* 初始化 */
    uart_led_indicator_ret_code_t ret = uart_led_indicator_init(&g_uart_led_config);
    if (ret != UART_LED_INDICATOR_OK) {
        printf("UART LED indicator init failed: %d\n", ret);
        return -1;
    }
    
    printf("UART LED indicator initialized in EVENT-DRIVEN mode\n");
    printf("No need to call uart_led_indicator_task() periodically!\n");
    
    return 0;
}

/**
 * @brief 兼容模式初始化示例（保持向后兼容）
 * 
 * @return int 0-成功，其他-失败
 */
int uart_led_indicator_compatible_init_example(void)
{
    /* 配置兼容模式（不提供回调函数） */
    g_uart_led_config.p_led_handler = &g_led_handler;
    g_uart_led_config.led_which = LED_485;
    g_uart_led_config.timeout_ms = 100;  /* 100ms超时 */
    g_uart_led_config.p_timebase_interface = &led_handler_timebase_interface;
    g_uart_led_config.state_callback = NULL;  /* 不提供回调函数，使用兼容模式 */
    g_uart_led_config.p_user_data = NULL;
    
    /* 初始化 */
    uart_led_indicator_ret_code_t ret = uart_led_indicator_init(&g_uart_led_config);
    if (ret != UART_LED_INDICATOR_OK) {
        printf("UART LED indicator init failed: %d\n", ret);
        return -1;
    }
    
    printf("UART LED indicator initialized in COMPATIBLE mode\n");
    printf("Need to call uart_led_indicator_task() periodically in main loop\n");
    
    return 0;
}

/**
 * @brief 串口接收中断处理示例
 * 
 * @param huart UART句柄
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART2) {  /* 假设使用USART2 */
        /* 处理接收到的数据 */
        uint8_t received_data = huart->pRxBuffPtr[0];
        
        /* 增加接收计数 */
        g_rx_data_count++;
        
        /* 通知LED指示器有数据接收 */
        uart_led_indicator_on_data_received();
        
        /* 继续接收下一个字节 */
        HAL_UART_Receive_IT(huart, &received_data, 1);
    }
}

/**
 * @brief 主循环示例（事件驱动模式）
 */
void main_loop_event_driven_example(void)
{
    /* 初始化事件驱动模式 */
    if (uart_led_indicator_event_driven_init_example() != 0) {
        return;
    }
    
    /* 主循环 */
    while (1) {
        /* 
         * 事件驱动模式下，无需调用uart_led_indicator_task()
         * LED的超时控制完全由bsp_led_handler的定时器机制处理
         * 状态变化通过回调函数通知
         */
        
        /* 处理其他任务 */
        // other_tasks();
        
        /* 延时 */
        HAL_Delay(10);
    }
}

/**
 * @brief 主循环示例（兼容模式）
 */
void main_loop_compatible_example(void)
{
    /* 初始化兼容模式 */
    if (uart_led_indicator_compatible_init_example() != 0) {
        return;
    }
    
    /* 主循环 */
    while (1) {
        /* 兼容模式下需要周期性调用task函数 */
        uart_led_indicator_task();
        
        /* 处理其他任务 */
        // other_tasks();
        
        /* 延时 */
        HAL_Delay(10);
    }
}

/**
 * @brief 性能对比测试
 */
void performance_comparison_example(void)
{
    printf("=== UART LED Indicator Performance Comparison ===\n");
    printf("\n1. Event-Driven Mode (Recommended):\n");
    printf("   - CPU Usage: ~0%% (no polling)\n");
    printf("   - Memory: +16 bytes (callback pointers)\n");
    printf("   - Real-time: Excellent (immediate response)\n");
    printf("   - Power: Lower (no periodic wake-up)\n");
    
    printf("\n2. Compatible Mode (Legacy):\n");
    printf("   - CPU Usage: ~0.1%% (10ms polling)\n");
    printf("   - Memory: Same as before\n");
    printf("   - Real-time: Good (10ms delay)\n");
    printf("   - Power: Higher (periodic wake-up)\n");
    
    printf("\n3. Resource Savings with Event-Driven Mode:\n");
    printf("   - Eliminates periodic task calls\n");
    printf("   - Reduces system wake-up frequency\n");
    printf("   - Better real-time response\n");
    printf("   - Lower power consumption\n");
}

//******************************** Functions ********************************//
