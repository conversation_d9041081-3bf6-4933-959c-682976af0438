/**
 * @file stack_test.c
 * @brief 栈监控功能测试
 * <AUTHOR> Assistant
 * @date 2024
 */

#include "FreeRTOS.h"
#include "task.h"
#include "stack_monitor.h"
#include "stack_overflow_debug.h"
#include <stdio.h>

/**
 * @brief 栈监控测试任务
 */
void stack_test_task(void *pvParameters)
{
    printf("[STACK_TEST] 栈监控测试任务启动\r\n");
    
    /* 打印初始栈使用情况 */
    PRINT_CURRENT_TASK_STACK();
    
    /* 等待一段时间让其他任务启动 */
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    /* 执行栈溢出排查 */
    printf("[STACK_TEST] 开始执行栈溢出排查...\r\n");
    stack_overflow_debug_main();
    
    /* 检查特定任务的栈安全性 */
    printf("[STACK_TEST] 检查FFT任务栈安全性...\r\n");
    if (check_task_stack_safety("fft_collection_data_thread") == pdFALSE)
    {
        printf("[STACK_TEST] ERROR: FFT任务栈使用不安全！\r\n");
    }
    else
    {
        printf("[STACK_TEST] FFT任务栈使用正常\r\n");
    }
    
    /* 定期打印栈使用情况 */
    uint32_t counter = 0;
    while(1)
    {
        vTaskDelay(pdMS_TO_TICKS(30000));  /* 30秒 */
        
        printf("[STACK_TEST] === 第 %d 次栈使用检查 ===\r\n", ++counter);
        print_all_tasks_stack_usage();
        
        /* 检查当前任务栈 */
        CHECK_CURRENT_TASK_STACK();
    }
}

/**
 * @brief 创建栈测试任务
 */
void create_stack_test_task(void)
{
    BaseType_t xReturn = xTaskCreate(
        stack_test_task,             /* 任务函数 */
        "StackTest",                /* 任务名称 */
        512,                        /* 栈大小 */
        NULL,                       /* 任务参数 */
        1,                          /* 任务优先级 */
        NULL                        /* 任务句柄 */
    );
    
    if (xReturn == pdPASS)
    {
        printf("[STACK_TEST] 栈测试任务创建成功\r\n");
    }
    else
    {
        printf("[STACK_TEST] ERROR: 栈测试任务创建失败\r\n");
    }
}

/**
 * @brief 快速栈检查函数
 */
void quick_stack_check(void)
{
    printf("[STACK_TEST] === 快速栈检查 ===\r\n");
    
    /* 检查当前任务 */
    TaskHandle_t xCurrentTask = xTaskGetCurrentTaskHandle();
    char* pcTaskName = pcTaskGetName(xCurrentTask);
    print_task_stack_usage(xCurrentTask, pcTaskName);
    
    /* 检查系统内存 */
    size_t xFreeHeapSize = xPortGetFreeHeapSize();
    printf("[STACK_TEST] 当前可用堆内存: %d 字节\r\n", (int)xFreeHeapSize);
    
    printf("[STACK_TEST] === 快速栈检查完成 ===\r\n");
}
