/******************************************************************************
 * @file bsp_led_handler.h
 * @brief LED处理器头文件，支持LED数量管理、注册、开关、定时、呼吸灯等功能
 * <AUTHOR>
 * @version 1.1
 * @date 2024-10-31
 *
 * @copyright Copyright (c) 2024
 *
 * Processing flow (事件驱动架构):
 * 1. LED处理器初始化 - 创建事件队列和处理任务
 * 2. LED驱动注册 - 可通过事件队列或直接调用
 * 3. LED控制请求 - 统一通过事件队列发送
 * 4. LED任务处理 - led_handler_task统一处理所有事件和状态更新
 * 5. 线程安全保证 - 所有LED操作都在同一任务中执行
 *
 * @par Usage Notes
 * - 使用bsp_led_handler_control()发送LED控制命令（线程安全）
 * - 使用bsp_led_handler_send_event()发送自定义事件
 * - 所有LED操作都是异步的，通过事件队列处理
 *
 * @par Usage Example
 * ```c
 * // 控制LED闪烁（线程安全）
 * led_control_param_t control_param = {
 *     .led_which = LED_1,
 *     .mode = LED_MODE_BLINK,
 *     .cycle_time_ms = 1000,
 *     .blink_count = 5,
 *     .proportion = LED_BLINK_RATIO_1_1
 * };
 * bsp_led_handler_control(&led_handler, &control_param);
 * ```
 *
 * @par dependencies
 * - bsp_led_driver.h
 * - linked_list.h
 * - RTOS接口
 *
 * @note 1 tab == 4 spaces!
 *
 *****************************************************************************/
#ifndef __BSP_LED_HANDLER_H_
#define __BSP_LED_HANDLER_H_
//******************************** Includes *********************************//

#include <stdint.h>
#include <stdbool.h>
#include "bsp_led_driver.h"
#include "linked_list.h"
#include "elog.h"
#include "system_config.h"
//******************************** Includes *********************************//

//******************************** Defines **********************************//
#define LED_HANDLER_RTOS_SUPPORTING
#define LED_HANDLER_USE_DYNAMIC_ALLOCATION

/* 调试宏定义已迁移到 system_config.h 中统一管理 */
#ifdef LED_HANDLER_DEBUG
    #define LED_HANDLER_LOG_D
    #define LED_HANDLER_LOG_E
    #define LED_HANDLER_LOG_I

    #ifdef LED_HANDLER_LOG_D
        #define LED_HANDLER_LOG_DEBUG(...) log_d(__VA_ARGS__)
    #else
        #define LED_HANDLER_LOG_DEBUG(...)
    #endif

    #ifdef LED_HANDLER_LOG_E
        #define LED_HANDLER_LOG_ERROR(...) log_e(__VA_ARGS__)
    #else
        #define LED_HANDLER_LOG_ERROR(...)
    #endif

    #ifdef LED_HANDLER_LOG_I
        #define LED_HANDLER_LOG_INFO(...) log_i(__VA_ARGS__)
    #else
        #define LED_HANDLER_LOG_INFO(...)
    #endif
#else
    #define LED_HANDLER_LOG_DEBUG(...)
    #define LED_HANDLER_LOG_ERROR(...)
    #define LED_HANDLER_LOG_INFO(...)
#endif

#define LED_HANDLER_MAX_DELAY          (0xFFFFFFFF)     /* 最大延时时间 */
#define LED_STOP_BLINKS                (0)              /* 停止闪烁     */
#define LED_MAX_BLINKS                 (0xFFFFFFFF)     /* 最大闪烁次数 */
#define LED_HANDLER_TASK_STACK_SIZE    (512)            /* 任务栈大小   */
#define LED_HANDLER_TASK_PRIORITY      (10)             /* 任务优先级   */
#define LED_HANDLER_EVENT_QUEUE_SIZE   (32)             /* 事件队列大小 */
#define LED_HANDLER_UPDATE_PERIOD_MS   (10)             /* 更新周期10ms */

#ifndef LED_HANDLER_USE_DYNAMIC_ALLOCATION
/* 最多能操作的led_handler数量 */
#define LED_HANDLER_MAX_NUM_OF_LEDS   (10)
#endif

#ifdef LED_HANDLER_RTOS_SUPPORTING
typedef enum
{
	LED_HANDLER_PDFALSE = 0,
	LED_HANDLER_PDTRUE  = 1
}led_handler_rtos_ret_code_t;
#endif /*	END OF RTOS_SUPPORTING */

typedef enum
{
  LED_HANDLER_OK             = 0,          /* Operation completed successfully.  */
  LED_HANDLER_ERROR          = 1,          /* Run-time error without case matched*/
  LED_HANDLER_ERRORTIMEOUT   = 2,          /* Operation failed with timeout      */
  LED_HANDLER_ERRORRESOURCE  = 3,          /* Resource not available.            */
  LED_HANDLER_ERRORPARAMETER = 4,          /* Parameter error.                   */
  LED_HANDLER_ERRORNOMEMORY  = 5,          /* Out of memory.                     */
  LED_HANDLER_RESERVED       = 0x7FFFFFFF  /* Reserved                           */
} led_handler_ret_code_t;

/* LED闪烁比例定义 */
typedef enum
{
    LED_BLINK_RATIO_1_9 = 0,    /* 亮1暗9 (10%占空比) */
    LED_BLINK_RATIO_1_4 = 1,    /* 亮1暗4 (20%占空比) */
    LED_BLINK_RATIO_1_1 = 2,    /* 亮1暗1 (50%占空比) */
    LED_BLINK_RATIO_4_1 = 3,    /* 亮4暗1 (80%占空比) */
    LED_BLINK_RATIO_9_1 = 4,    /* 亮9暗1 (90%占空比) */
}led_blink_proportion_t;

/* LED控制模式 */
typedef enum
{
    LED_MODE_OFF = 0,           /* 关闭 */
    LED_MODE_ON,                /* 常亮 */
    LED_MODE_BLINK,             /* 闪烁 */
    LED_MODE_BREATH,            /* 呼吸灯 */
    LED_MODE_TIMER_ON,          /* 定时亮起 */
    LED_MODE_TIMER_OFF,         /* 定时关闭 */
    LED_MODE_PWM,               /* PWM调光 */
}led_control_mode_t;

/* LED事件类型 */
typedef enum
{
    LED_EVENT_CONTROL = 0,      /* LED控制事件 */
    LED_EVENT_REGISTER,         /* LED注册事件 */
    LED_EVENT_UNREGISTER,       /* LED注销事件 */
    LED_EVENT_UPDATE,           /* LED状态更新事件 */
}led_event_type_t;



//******************************** Defines **********************************//

//******************************** Declaring ********************************//
/*                 From OS层：       RTOS 接口                                */

#ifdef LED_HANDLER_RTOS_SUPPORTING
typedef void (*task_function_t)(void *);
// 定义一个结构体，用于表示RTOS的接口
typedef struct led_handler_os_interface_t
{
    // 创建任务
    led_handler_ret_code_t (*rtos_task_create)   (task_function_t task_function,
                                                  const char * const task_name,
                                                  const uint16_t stack_size,
                                                  void * const task_argument,
                                                  uint32_t priority,
                                                  void ** const task_handle);
    // 删除任务
    led_handler_ret_code_t (*rtos_task_delete)   (void * const task_handle);
    //队列创建
    led_handler_ret_code_t (*rtos_queue_create)  (void ** const pqueue,
                                                  uint32_t queue_size,
                                                  uint32_t item_size);
    //队列删除
    led_handler_ret_code_t (*rtos_queue_delete)  (void * const pqueue);
    //队列发送
    led_handler_ret_code_t (*rtos_queue_send)    (void * const pqueue,
                                                  void * const item,
                                                  uint32_t timeout);
    //队列发送中断形式
    led_handler_ret_code_t (*rtos_queue_send_fromisr)(void * const pqueue,
                                                      void * const item,
                                                      void * const pxHigherPriorityTaskWoken);
    //队列接收
    led_handler_ret_code_t (*rtos_queue_receive) (void * const pqueue,
                                                  void * const item,
                                                  uint32_t timeout);
    // 创建互斥量
    led_handler_ret_code_t (*rtos_mutex_create)  (void ** const pmutex);
    // 删除互斥量
    led_handler_ret_code_t (*rtos_mutex_delete)  (void * const pmutex);
    // 获取互斥量
    led_handler_ret_code_t (*rtos_mutex_take)    (void * const pmutex, uint32_t timeout);
    // 释放互斥量
    led_handler_ret_code_t (*rtos_mutex_give)    (void * const pmutex);
}led_handler_os_interface_t;

typedef struct
{
	led_handler_rtos_ret_code_t (*pf_rtos_critical_enter)(void);
	led_handler_rtos_ret_code_t (*pf_rtos_critical_exit) (void);
}led_handler_os_critical_t;
#endif /*	END OF LED_HANDLER_RTOS_SUPPORTING */

#ifdef LED_HANDLER_USE_DYNAMIC_ALLOCATION
  /* From Core层：   动态分配 接口        */
typedef struct
{
  void *(*pfmalloc)   (uint32_t mem_size);
  void (*pffree)      (void *p_mem);
}led_handler_dynamic_allocation_t;
/* From Core层：   动态分配 接口        */
#endif

/* From Core层：     时基接口                                 */
typedef struct
{
    uint32_t (*pfget_timetick_ms) (void);
}led_handler_timebase_interface_t;

/* LED控制参数结构体 */
typedef struct
{
    led_which_t                led_which;          /* LED编号 */
    led_control_mode_t         mode;               /* 控制模式 */
    uint32_t                   cycle_time_ms;      /* 周期时间(ms) */
    uint32_t                   blink_count;        /* 闪烁次数，0表示无限次 */
    led_blink_proportion_t     proportion;         /* 闪烁比例 */
    uint32_t                   timer_duration_ms;  /* 定时时长(ms) */
    uint8_t                    brightness;         /* 亮度(0-100) */
    uint32_t                   breath_period_ms;   /* 呼吸周期(ms) */

    /* 回调函数支持 - 每个LED控制请求可以有独立的回调 */
    pf_led_event_callback_t    event_callback;     /* 状态变化回调函数(可选) */
    void                      *p_callback_param;   /* 回调函数参数(可选) */
}led_control_param_t;

/* LED事件结构体 */
typedef struct
{
    led_event_type_t           event_type;         /* 事件类型 */
    led_control_param_t        control_param;      /* 控制参数 */
    bsp_led_driver_t          *p_led_driver;       /* LED驱动指针(注册时使用) */
    uint32_t                   timestamp;          /* 时间戳 */
}led_handler_event_t;

/* LED处理器系统配置 */
typedef struct
{
    uint8_t                    led_handler_task_priority;      /* 任务优先级 */
    uint16_t                   led_handler_task_stack_size;    /* 任务栈大小 */
    uint16_t                   led_handler_event_queue_size;   /* 事件队列大小 */
}led_handler_system_config_t;

/* LED处理器输入参数 */
typedef struct
{
    led_handler_system_config_t      *p_system_config;         /* 系统配置 */
    led_handler_os_interface_t       *p_led_os_interface;      /* RTOS接口 */
    led_handler_os_critical_t        *p_led_os_critical;       /* 临界区接口 */
    led_handler_dynamic_allocation_t *p_led_dynamic_allocation; /* 动态分配接口 */
    led_handler_timebase_interface_t *p_led_timebase_interface; /* 时基接口 */
}led_handler_input_all_arg_t;

/* LED处理器私有数据前向声明 */
typedef struct bsp_led_handler_priv_data_t bsp_led_handler_priv_data_t;

/* LED处理器主结构体 */
typedef struct bsp_led_handler_t
{
    /* 系统配置 */
    led_handler_system_config_t      *p_system_config;
    /* RTOS接口 */
    led_handler_os_interface_t       *p_led_os_interface;
    /* 临界区接口 */
    led_handler_os_critical_t        *p_led_os_critical;
    /* 动态分配接口 */
    led_handler_dynamic_allocation_t *p_led_dynamic_allocation;
    /* 时基接口 */
    led_handler_timebase_interface_t *p_led_timebase_interface;

    /* LED处理任务句柄 */
    void                             *led_handler_task;
    /* LED事件队列句柄 */
    void                             *led_handler_event_queue;
    /* LED处理器互斥量 */
    void                             *led_handler_mutex;

    /* LED处理器私有数据 */
    bsp_led_handler_priv_data_t      *p_private_data;
}bsp_led_handler_t;

/* LED处理器事件回调函数类型 */
typedef void (*pf_led_event_callback_t)(led_which_t led_which, led_control_mode_t mode, void *p_param);

/* 函数声明 */
/* LED处理器初始化 */
led_handler_ret_code_t bsp_led_handler_instance(
    bsp_led_handler_t           *p_led_handler,
    led_handler_input_all_arg_t *p_led_handler_input
);

/* LED驱动注册 */
led_handler_ret_code_t bsp_led_handler_register_driver(
    bsp_led_handler_t *p_led_handler,
    bsp_led_driver_t  *p_led_driver
);

/* LED驱动注销 */
led_handler_ret_code_t bsp_led_handler_unregister_driver(
    bsp_led_handler_t *p_led_handler,
    led_which_t       led_which
);

void led_handler_task(void *argument);

/* LED控制接口 - 事件驱动方式（线程安全）
 * 此函数将LED控制请求封装为事件发送到队列，由led_handler_task统一处理
 * 确保所有LED操作都在同一任务中执行，保证线程安全
 */
led_handler_ret_code_t bsp_led_handler_control(
    bsp_led_handler_t      *p_led_handler,
    led_control_param_t    *p_control_param,
    bool                   status
);

/* 获取已注册LED数量 */
uint8_t bsp_led_handler_get_registered_count(bsp_led_handler_t *p_led_handler);


/* LED事件处理函数 */
led_handler_ret_code_t bsp_led_handler_send_event(
    bsp_led_handler_t   *p_led_handler,
    led_handler_event_t *p_event
);

#endif // __BSP_LED_HANDLER_H_
