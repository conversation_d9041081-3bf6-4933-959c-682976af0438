#include <stddef.h>
#include <stdio.h>
#include <string.h>

#include "uart_dev.h"

#include "FreeRTOS.h"
#include "semphr.h"
#include "queue.h"
#include "string.h"

#define CONFIG_USE_UART_IDLE_IRQ
#define UART_RX_QUEUE_LEN 128
#define UART_RX_BUF_LEN   128



// 定义一个结构体Uart_private_data
typedef struct 
{
	//串口号
	uart_port e_uart_port;
	// 串口句柄
	puart_handle_t psuart_handle;
	// 信号量 sem
	SemaphoreHandle_t xTxSem;
    // 队列 queue
    QueueHandle_t xRxQueue;
    //递归锁
    SemaphoreHandle_t xRecursiveMutex;
    // 接收到的数据
    uint8_t rxdata[UART_RX_BUF_LEN];
}Uart_private_data,*PUart_private_data;


static Uart_private_data g_uart_private_data[uart_port_quantity] =
{
	{
		.psuart_handle = NULL,
		.xTxSem = NULL,
        .xRxQueue = NULL,
        .rxdata = 0,
	},
};
/**
 * @brief 串口初始化函数
 * 
 * @param puart_dev 串口设备
 * @return -1 初始化失败
 *          0 初始化成功
 */
static int uart_init(struct Uart_dev *puart_dev)
{
   PUart_private_data psdata = puart_dev->private_data;
   //如果需要添加串口则在这里添加
   if(0 == strcmp(puart_dev->name,"uart2"))
   {
       psdata->e_uart_port = uart_port2;
   }
   psdata->psuart_handle = get_uarthandle();
	 psdata->psuart_handle->uartinit();
   psdata->xTxSem = xSemaphoreCreateBinary();
   psdata->xRxQueue = xQueueCreate(UART_RX_QUEUE_LEN, 1);
   psdata->xRecursiveMutex = xSemaphoreCreateRecursiveMutex();
   if (NULL == psdata->psuart_handle &&
	   NULL == psdata->xTxSem        &&
	   NULL == psdata->xRxQueue)
	   {
		return -1;
	   }
	   /* 启动第1次数据的接收 */
#ifdef CONFIG_USE_UART_IDLE_IRQ
       psdata->psuart_handle->uartreceiveidle_irq(psdata->psuart_handle->uart_msg.ps_uart[psdata->e_uart_port], psdata->rxdata, UART_RX_BUF_LEN);
#else
	   psdata->psuart_handle->uartreceive_irq(psdata->psuart_handle->uart_msg.ps_uart[psdata->e_uart_port], psdata->rxdata, 1);
#endif
      

   return 0;
}

/**
 * @brief 串口发送函数
 * 
 * @param puart_dev 串口设备
 * @param datas 发送的数据
 * @param len 发送数据的长度
 * @param timeout_ms 超时时间
 * @return int 0 发送成功
 *            -1 发送失败
 */
static int uart_send(struct Uart_dev* puart_dev, uint8_t *datas, int len, uint32_t timeout_ms)
{
    // 获取Uart设备的私有数据
    PUart_private_data psdata = puart_dev->private_data;
    
    /* 仅仅是触发中断而已 */
    psdata->psuart_handle->uartsend_irq(psdata->psuart_handle->uart_msg.ps_uart[psdata->e_uart_port], datas, len);

    /* 等待发送完毕:等待信号量 */
    if (pdTRUE == xSemaphoreTake(psdata->xTxSem, timeout_ms))
        return 0;
    else
        return -1;
}

/**
 * @brief 串口接收函数
 * 
 * @param puart_dev 串口设备
 * @param data 接收到的数据
 * @param timeout_ms 超时时间
 * @return int 0 接收成功
 *            -1 接收失败
 **/
static int uart_recv(struct Uart_dev* puart_dev, uint8_t *data, int timeout_ms)
{
    // 获取 Uart_dev 类型指针的私有数据
    PUart_private_data psdata = puart_dev->private_data;
    
    /* 读取队列得到数据, 问题:谁写队列?中断:写队列 */
    if (pdPASS == xQueueReceive(psdata->xRxQueue, data,timeout_ms))
        return 0;
    else
        return -1;
}



static Uart_dev g_uart[uart_port_quantity]=
{
	{
		.name = "uart2",
        .uart_init = uart_init,
        .uart_send = uart_send,
        .uart_receive = uart_recv,
        .private_data = &g_uart_private_data[uart_port2],
	},
};

//串口设备链表
static PNode g_p_uart_list;

/**
 * @brief 增加串口设备
 * 
 */
void ADDUARTDEVICE(void)
{
	ADDLinked_List(&g_p_uart_list, &g_uart[uart_port2].p_UARTDev);
}

/**
 * @brief 获取串口设备
 * 
 * @param e_uart_port 串口端口
 * @return PUart_dev 串口设备
 *         NULL 没有找到	
 */
PUart_dev get_uart_dev(char* name)
{
	PNode ptemp = g_p_uart_list;
	// 遍历uart_list链表
	while(ptemp)
	{
		//从链表头找到第一个uart设备地址
		PUart_dev pdev = container_of(ptemp, Uart_dev, p_UARTDev);
        // 如果找到的uart_port与参数e_uart_port相等
        if(0 == strcmp(name, pdev->name))
        {
            return pdev;
        }
        ptemp = ptemp->pNext;
	}
	return NULL;
}


//以下为STM32串口的回调函数，没有解耦
extern UART_HandleTypeDef huart2;
void HAL_UART_TxCpltCallback(UART_HandleTypeDef *huart)
{
    PUart_private_data psdata;
    
    if (huart == &huart2)
    {
        psdata = g_uart[uart_port2].private_data;
        
        /* 释放信号量 */
        xSemaphoreGiveFromISR(psdata->xTxSem, NULL);
    }
}

void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    PUart_private_data psdata;
    uint16_t len = 0;
    if (huart == &huart2)
    {
        len = huart->RxXferSize - huart->RxXferCount;
        //接收数据时需要点亮LED灯
        #if CONFIG_LED_ON_RECV
        led_event_type event = LED_EVENT_ON;
        led_event_handle_send(event, LED_EVENT_IRQ);
        #endif
        psdata = g_uart[uart_port2].private_data;
        
        /* 写队列 */
        for(int i = 0; i < len; i++)
        {
            xQueueSendFromISR(psdata->xRxQueue, &psdata->rxdata[i], NULL);
        }
    
        /* 再次启动数据的接收 */
        HAL_UART_Receive_IT(psdata->psuart_handle->uart_msg.ps_uart[psdata->e_uart_port], psdata->rxdata, len);
    }
}

void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
    PUart_private_data psdata;
    if (huart == &huart2)
    {
        psdata = g_uart[uart_port2].private_data;
        /* 写队列 */
        for(int i = 0; i < Size; i++)
        {
            xQueueSendFromISR(psdata->xRxQueue, &psdata->rxdata[i], NULL);
        }
        /* 再次启动数据的接收 */
        psdata->psuart_handle->uartreceiveidle_irq(psdata->psuart_handle->uart_msg.ps_uart[psdata->e_uart_port], psdata->rxdata, huart->RxXferSize);
    }
}

void HAL_UART_ErrorCallback(UART_HandleTypeDef *huart)
{
    PUart_private_data psdata;
    if (huart == &huart2)
    {
        psdata = g_uart[uart_port2].private_data;
        psdata->psuart_handle->uartreceiveidle_irq(psdata->psuart_handle->uart_msg.ps_uart[psdata->e_uart_port], psdata->rxdata, huart->RxXferSize);
    }
}